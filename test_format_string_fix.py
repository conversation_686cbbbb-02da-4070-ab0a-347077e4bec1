#!/usr/bin/env python3
"""
Test the format string fix for the unit extraction template
"""

def test_format_string_fix():
    """Test that the template string formatting works correctly"""
    print("🔧 TESTING FORMAT STRING FIX")
    print("=" * 60)
    
    # Test the problematic template section that was causing the error
    template_section = '''  "unit_generation_mwh": [{{"value": "2500000", "year": "2023"}}, {{"value": "2400000", "year": "2022"}}],
  "plant_generation_mwh": [{{"value": "10000000", "year": "2023"}}, {{"value": "9600000", "year": "2022"}}],
  "fuel_consumed_tons": [{{"value": "1200000", "year": "2023"}}, {{"value": "1150000", "year": "2022"}}],
  "annual_emission_mt": [{{"value": "2.5", "year": "2023"}}, {{"value": "2.4", "year": "2022"}}],
  "emission_factor_calculated": [{{"value": "0.92", "year": "2023"}}, {{"value": "0.94", "year": "2022"}}]'''
    
    # Test variables that would be used in the format call
    test_vars = {
        'unit_number': '1',
        'plant_context': {'plant_name': 'Antelope Valley Station'},
        'country': 'United States',
        'summaries': 'Test research data'
    }
    
    try:
        # Try to format the template section (this should work now)
        formatted = template_section.format(**test_vars)
        print("✅ Template formatting SUCCESSFUL!")
        print("✅ No 'Invalid format specifier' error")
        
        # Check that the double braces became single braces in the output
        if '{"value": "2500000", "year": "2023"}' in formatted:
            print("✅ Double braces correctly converted to single braces")
        else:
            print("❌ Double braces not converted correctly")
            return False
            
        # Show a sample of the formatted output
        print(f"\n📄 Sample formatted output:")
        lines = formatted.split('\n')
        for line in lines[:3]:
            print(f"   {line}")
        print("   ...")
        
        return True
        
    except Exception as e:
        print(f"❌ Template formatting FAILED: {e}")
        return False

def test_original_error_case():
    """Test the specific case that was causing the original error"""
    print("\n🚨 TESTING ORIGINAL ERROR CASE")
    print("=" * 60)
    
    # This was the problematic line that caused the error
    problematic_line_old = '"unit_generation_mwh": [{"value": "2500000", "year": "2023"}, {"value": "2400000", "year": "2022"}],'
    problematic_line_fixed = '"unit_generation_mwh": [{{"value": "2500000", "year": "2023"}}, {{"value": "2400000", "year": "2022"}}],'
    
    print("❌ OLD (problematic) line:")
    print(f"   {problematic_line_old}")
    
    print("✅ FIXED line:")
    print(f"   {problematic_line_fixed}")
    
    # Test the old line (should fail)
    try:
        formatted_old = problematic_line_old.format()
        print("❌ OLD line should have failed but didn't!")
        return False
    except Exception as e:
        print(f"✅ OLD line correctly fails with: {type(e).__name__}")
    
    # Test the fixed line (should work)
    try:
        formatted_fixed = problematic_line_fixed.format()
        print("✅ FIXED line works correctly!")
        print(f"   Result: {formatted_fixed}")
        return True
    except Exception as e:
        print(f"❌ FIXED line still fails: {e}")
        return False

if __name__ == "__main__":
    print("🚀 FORMAT STRING FIX TESTING")
    print("=" * 80)
    
    # Test 1: Template formatting
    format_ok = test_format_string_fix()
    
    # Test 2: Original error case
    error_case_ok = test_original_error_case()
    
    print("\n" + "=" * 80)
    print("📋 TEST SUMMARY:")
    print(f"   Template Formatting: {'✅ PASS' if format_ok else '❌ FAIL'}")
    print(f"   Original Error Case: {'✅ PASS' if error_case_ok else '❌ FAIL'}")
    
    if format_ok and error_case_ok:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ The format string error has been FIXED!")
        print("✅ Unit extraction should now work without format errors")
        print("\n💡 The issue was single braces in JSON templates")
        print("💡 Fixed by changing { } to {{ }} to escape them properly")
    else:
        print("\n⚠️ SOME TESTS FAILED - ISSUES REMAIN")
