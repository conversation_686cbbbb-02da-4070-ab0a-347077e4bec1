#!/usr/bin/env python3
"""
Test the complete USA Excel calculation engine with all PLF cases
and verify unit processing logic
"""

import sys
import os
sys.path.append('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH-feat-agi-tech-int-007')

from usa_excel_calculation_engine import create_usa_excel_calculation_engine
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_all_plf_cases():
    """Test all PLF calculation cases"""
    print("🧪 TESTING ALL PLF CALCULATION CASES")
    print("=" * 60)
    
    # Create the USA Excel calculation engine
    calc_engine = create_usa_excel_calculation_engine()
    
    # Test data for different cases
    test_cases = [
        {
            "name": "Case 1: Plant Level → Unit Level (Current Implementation)",
            "unit_data": {
                "unit_number": "1",
                "capacity": 477,
                "technology": "subcritical"
            }
        },
        {
            "name": "Case 2: Direct Unit Level (with unit generation data)",
            "unit_data": {
                "unit_number": "2", 
                "capacity": 477,
                "technology": "subcritical",
                "unit_generation_mwh": 2500000  # 2.5 TWh per year
            }
        },
        {
            "name": "Case 4: Net to Gross Conversion",
            "unit_data": {
                "unit_number": "1",
                "capacity": 477,
                "technology": "subcritical",
                "net_generation_mwh": 2300000  # 2.3 TWh net generation
            }
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🔬 TEST {i}: {test_case['name']}")
        print("-" * 50)
        
        try:
            # Test the calculation
            result = calc_engine.calculate_unit_parameters_usa("Antelope Valley", test_case["unit_data"])
            
            if result:
                print(f"✅ Calculation successful!")
                print(f"📊 PLF Array Length: {len(result.get('plf', []))}")
                print(f"📊 Auxiliary Power Array Length: {len(result.get('auxiliary_power_consumed', []))}")
                print(f"📊 Gross Generation Array Length: {len(result.get('gross_power_generation', []))}")
                
                # Show sample values
                plf_values = result.get('plf', [])
                if plf_values:
                    sample_plf = [f"{val.get('value', 0)*100:.1f}%" for val in plf_values[:3]]
                    print(f"📊 Sample PLF Values: {sample_plf}...")
                
                aux_values = result.get('auxiliary_power_consumed', [])
                if aux_values:
                    sample_aux = [f"{val.get('value', 0)*100:.1f}%" for val in aux_values[:3]]
                    print(f"📊 Sample Aux Power Values: {sample_aux}...")
                    
            else:
                print(f"❌ Calculation failed - no results returned")
                
        except Exception as e:
            print(f"❌ Test failed with error: {e}")
            import traceback
            traceback.print_exc()

def test_unit_detection():
    """Test how many units should be detected for Antelope Valley"""
    print("\n🔍 TESTING UNIT DETECTION FOR ANTELOPE VALLEY")
    print("=" * 60)
    
    # Check USA Details.xlsx for Antelope Valley unit information
    calc_engine = create_usa_excel_calculation_engine()
    
    try:
        # Extract plant data to see unit information
        plant_data = calc_engine.extract_plant_data_from_usa_excel("Antelope Valley")
        
        if plant_data:
            print(f"✅ Found plant data for Antelope Valley")
            print(f"📊 Years available: {list(plant_data.keys())}")
            
            # Check 2024 data for unit information
            if '2024' in plant_data:
                data_2024 = plant_data['2024']
                print(f"📊 2024 Data Keys: {list(data_2024.keys())}")
                print(f"📊 Total Net Generation 2024: {data_2024.get('total_net_generation', 0):,.0f} MWh")
                print(f"📊 Total Fuel Consumption 2024: {data_2024.get('total_fuel_consumption', 0):,.0f} tons")
                
                # Check if there are unit-specific records
                if 'unit_records' in data_2024:
                    unit_records = data_2024['unit_records']
                    print(f"📊 Number of unit records: {len(unit_records)}")
                    for i, record in enumerate(unit_records):
                        unit_id = record.get('Unit ID', 'Unknown')
                        capacity = record.get('Nameplate Capacity (MW)', 'Unknown')
                        print(f"   Unit {i+1}: ID={unit_id}, Capacity={capacity} MW")
        else:
            print(f"❌ No plant data found for Antelope Valley")
            
    except Exception as e:
        print(f"❌ Unit detection test failed: {e}")
        import traceback
        traceback.print_exc()

def test_multiple_units():
    """Test processing multiple units for the same plant"""
    print("\n🏭 TESTING MULTIPLE UNIT PROCESSING")
    print("=" * 60)
    
    calc_engine = create_usa_excel_calculation_engine()
    
    # Test both units for Antelope Valley
    units_to_test = ["1", "2"]
    
    for unit_num in units_to_test:
        print(f"\n🔧 Processing Unit {unit_num}")
        print("-" * 30)
        
        unit_data = {
            "unit_number": unit_num,
            "capacity": 477,
            "technology": "subcritical"
        }
        
        try:
            result = calc_engine.calculate_unit_parameters_usa("Antelope Valley", unit_data)
            
            if result:
                print(f"✅ Unit {unit_num} calculation successful!")
                
                # Check if results are different (they should be the same for Case 1)
                plf_values = result.get('plf', [])
                if plf_values and len(plf_values) > 0:
                    first_plf = plf_values[0].get('value', 0) * 100
                    print(f"📊 Unit {unit_num} PLF (2024): {first_plf:.1f}%")
                    
                # Show the SK (sort key) that would be used for JSON filename
                sk = f"unit#coal#{unit_num}#plant#1"
                print(f"📄 Expected JSON filename: {sk}.json")
                
            else:
                print(f"❌ Unit {unit_num} calculation failed")
                
        except Exception as e:
            print(f"❌ Unit {unit_num} processing failed: {e}")

if __name__ == "__main__":
    print("🚀 COMPREHENSIVE USA EXCEL CALCULATION ENGINE TEST")
    print("=" * 80)
    
    # Test all PLF cases
    test_all_plf_cases()
    
    # Test unit detection
    test_unit_detection()
    
    # Test multiple unit processing
    test_multiple_units()
    
    print("\n✅ ALL TESTS COMPLETED")
