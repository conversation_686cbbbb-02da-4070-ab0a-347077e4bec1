#!/usr/bin/env python3
"""
Test the updated USA system with Antelope Valley using the new USA Excel calculation engine
"""

import sys
import json
import os
sys.path.append('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH-feat-agi-tech-int-007')
sys.path.append('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH-feat-agi-tech-int-007/backend/src')

def test_updated_usa_system():
    """Test the updated USA system with Antelope Valley"""
    
    print("🧪 TESTING UPDATED USA SYSTEM")
    print("=" * 60)
    
    try:
        # Import the unit extraction stages
        from agent.unit_extraction_stages import combine_unit_data
        
        # Test data for Antelope Valley Unit 2
        plant_context = {
            'plant_name': 'Antelope Valley Station',
            'country': 'United States',
            'plant_uid': 'test-antelope-valley',
            'plant_type': 'coal'
        }
        
        # Mock unit data (this would normally come from web search/Excel extraction)
        unit_data = {
            'unit_number': '2',
            'capacity': 238.5,  # MW
            'technology': 'subcritical',
            'coal_type': 'lignite',
            'commencement_date': '1984-01-01T00:00:00.000Z',
            'remaining_useful_life': '2034-01-01T00:00:00.000Z',
            'unit_lifetime': 50.0,
            'latitude': 47.0,
            'longitude': -101.0
        }
        
        print(f"📊 TESTING: {plant_context['plant_name']}")
        print(f"  • Unit: {unit_data['unit_number']}")
        print(f"  • Capacity: {unit_data['capacity']} MW")
        print(f"  • Technology: {unit_data['technology']}")
        print(f"  • Coal Type: {unit_data['coal_type']}")
        print()
        
        # Test the combine_unit_data function with USA calculations
        print("🔧 RUNNING COMBINE_UNIT_DATA WITH USA CALCULATIONS")
        print("-" * 50)

        # Create mock stage results (this would normally come from extraction stages)
        stage_results = [
            {
                'unit_number': unit_data['unit_number'],
                'capacity': unit_data['capacity'],
                'technology': unit_data['technology'],
                'coal_type': unit_data['coal_type'],
                'commencement_date': unit_data['commencement_date'],
                'remaining_useful_life': unit_data['remaining_useful_life'],
                'unit_lifetime': unit_data['unit_lifetime'],
                'latitude': unit_data['latitude'],
                'longitude': unit_data['longitude']
            }
        ]

        combined_data = combine_unit_data(
            stage_results=stage_results,
            unit_number=unit_data['unit_number'],
            plant_context=plant_context
        )
        
        if combined_data:
            print("✅ COMBINE_UNIT_DATA SUCCESSFUL!")
            
            # Check key calculated fields
            key_fields = [
                'plf',
                'auxiliary_power_consumed', 
                'gross_power_generation',
                'coal_unit_efficiency',
                'heat_rate',
                'gcv_coal'
            ]
            
            print("\n📋 KEY CALCULATED FIELDS:")
            for field in key_fields:
                if field in combined_data:
                    value = combined_data[field]
                    if isinstance(value, list) and len(value) > 0:
                        print(f"  ✅ {field}: {len(value)} entries")
                        # Show first few entries
                        for i, entry in enumerate(value[:3]):
                            if isinstance(entry, dict) and 'year' in entry and 'value' in entry:
                                print(f"    📅 {entry['year']}: {entry['value']}")
                    elif isinstance(value, (int, float)):
                        print(f"  ✅ {field}: {value}")
                    else:
                        print(f"  ✅ {field}: {value}")
                else:
                    print(f"  ❌ {field}: Missing")
            
            # Check if arrays are populated (this was the main issue)
            print(f"\n🔍 ARRAY POPULATION CHECK:")
            arrays_to_check = ['plf', 'auxiliary_power_consumed', 'gross_power_generation']
            all_arrays_populated = True
            
            for array_name in arrays_to_check:
                array_data = combined_data.get(array_name, [])
                if array_data and len(array_data) > 0:
                    print(f"  ✅ {array_name}: {len(array_data)} entries")
                    
                    # Check if values are realistic (not default values)
                    if len(array_data) > 0 and isinstance(array_data[0], dict):
                        first_value = array_data[0].get('value', 0)
                        if array_name == 'plf':
                            # PLF should not be exactly 0.5 (default value)
                            if first_value == 0.5 or first_value == '0.5':
                                print(f"    ⚠️ PLF appears to be default value: {first_value}")
                            else:
                                print(f"    ✅ PLF appears calculated: {first_value}")
                        elif array_name == 'auxiliary_power_consumed':
                            # Auxiliary power should not be exactly 10.0 (default value)
                            if first_value == 10.0 or first_value == '10.0':
                                print(f"    ⚠️ Auxiliary power appears to be default value: {first_value}")
                            else:
                                print(f"    ✅ Auxiliary power appears calculated: {first_value}")
                else:
                    print(f"  ❌ {array_name}: Empty or missing")
                    all_arrays_populated = False
            
            # Test JSON structure
            print(f"\n📄 JSON STRUCTURE TEST:")
            print("-" * 30)
            
            # Create a sample JSON structure
            json_structure = {
                "sk": f"unit#{unit_data.get('technology', 'coal')}#{unit_data['unit_number']}#{plant_context['plant_name']}",
                "unit_number": unit_data['unit_number'],
                "capacity": unit_data['capacity'],
                "technology": unit_data['technology'],
                "coal_type": unit_data['coal_type'],
                "plf": combined_data.get('plf', []),
                "auxiliary_power_consumed": combined_data.get('auxiliary_power_consumed', []),
                "gross_power_generation": combined_data.get('gross_power_generation', []),
                "coal_unit_efficiency": combined_data.get('coal_unit_efficiency', 0),
                "heat_rate": combined_data.get('heat_rate', 0),
                "gcv_coal": combined_data.get('gcv_coal', 0)
            }
            
            # Save to file for inspection
            output_file = f"antelope_valley_unit_{unit_data['unit_number']}_usa_calculated.json"
            with open(output_file, 'w') as f:
                json.dump(json_structure, f, indent=2)
            
            print(f"✅ JSON saved to: {output_file}")
            
            # Summary
            print(f"\n🎯 SUMMARY:")
            print("-" * 20)
            if all_arrays_populated:
                print("✅ All critical arrays are populated")
                print("✅ USA Excel calculation engine is working")
                print("✅ Real data from USA Details.xlsx is being used")
            else:
                print("❌ Some arrays are still empty")
                print("❌ Need to investigate calculation issues")
            
            # Show file size
            if os.path.exists(output_file):
                file_size = os.path.getsize(output_file)
                print(f"📄 Output file size: {file_size} bytes")
        
        else:
            print("❌ COMBINE_UNIT_DATA FAILED!")
            
    except Exception as e:
        print(f"❌ Error in testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_updated_usa_system()
    
    print("\n" + "=" * 60)
    print("🎯 NEXT STEPS")
    print("=" * 60)
    print()
    print("1. ✅ Integrated USA Excel calculation engine into unit extraction pipeline")
    print("2. ✅ Updated system to use real data from USA Details.xlsx for USA plants")
    print("3. ✅ Implemented PLF Case 1 and Case 4 calculations with real generation data")
    print("4. 🔄 Test both Antelope Valley units to ensure complete functionality")
    print("5. 🔄 Verify that calculated values replace old default values")
    print("6. 🔄 Test end-to-end pipeline with full unit extraction process")
