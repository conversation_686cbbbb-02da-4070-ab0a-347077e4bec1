#!/usr/bin/env python3
"""
Analyze the structure of USA Details.xlsx to understand the data format for calculations
"""

import pandas as pd
import sys

def analyze_usa_details_structure():
    """Analyze the USA Details.xlsx file structure"""
    
    print("🔍 ANALYZING USA DETAILS.XLSX STRUCTURE")
    print("=" * 60)
    
    try:
        excel_file = '/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH-feat-agi-tech-int-007-feat-agi-tech-int-007/USA Details.xlsx'
        
        # Get all sheet names
        xl_file = pd.ExcelFile(excel_file)
        sheet_names = xl_file.sheet_names
        print(f"📋 Available sheets: {sheet_names}")
        
        # 1. Analyze Coal yearly sheets for generation and fuel consumption data
        print(f"\n📊 ANALYZING COAL YEARLY SHEETS:")
        print("-" * 40)
        
        coal_sheets = ['Coal 2024', 'Coal 2023', 'Coal 2022', 'Coal 2021', 'Coal 2020']
        
        for sheet_name in coal_sheets:
            if sheet_name in sheet_names:
                print(f"\n🔧 {sheet_name}:")
                df = pd.read_excel(excel_file, sheet_name=sheet_name)
                
                print(f"  • Total records: {len(df)}")
                print(f"  • Columns: {list(df.columns)}")
                
                # Look for generation and fuel consumption columns
                generation_cols = [col for col in df.columns if 'generation' in col.lower() or 'megawatt' in col.lower()]
                fuel_cols = [col for col in df.columns if 'fuel' in col.lower() or 'consumption' in col.lower()]
                
                print(f"  • Generation columns: {generation_cols}")
                print(f"  • Fuel consumption columns: {fuel_cols}")
                
                # Check for Antelope Valley Station specifically
                antelope_data = df[df['Plant Name'].str.contains('Antelope Valley', na=False, case=False)]
                if not antelope_data.empty:
                    print(f"  • Antelope Valley records: {len(antelope_data)}")
                    
                    # Show sample data for key columns
                    key_columns = ['Plant Name', 'Unit ID'] + generation_cols + fuel_cols
                    available_columns = [col for col in key_columns if col in df.columns]
                    
                    if available_columns:
                        print(f"  • Sample Antelope Valley data:")
                        sample_data = antelope_data[available_columns].head(2)
                        for idx, row in sample_data.iterrows():
                            print(f"    Unit {row.get('Unit ID', 'N/A')}:")
                            for col in available_columns[2:]:  # Skip Plant Name and Unit ID
                                if pd.notna(row[col]):
                                    print(f"      {col}: {row[col]}")
        
        # 2. Analyze Assumptions sheet for auxiliary power data
        print(f"\n📊 ANALYZING ASSUMPTIONS SHEET:")
        print("-" * 40)
        
        if 'Assumptions' in sheet_names:
            assumptions_df = pd.read_excel('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH-feat-agi-tech-int-007/current state calculations.xlsx', sheet_name='Assumptions')
            print(f"  • Assumptions sheet columns: {list(assumptions_df.columns)}")
            
            # Look for auxiliary power data
            aux_cols = [col for col in assumptions_df.columns if 'aux' in col.lower() or 'auxiliary' in col.lower()]
            print(f"  • Auxiliary power columns: {aux_cols}")
            
            if aux_cols:
                print(f"  • Sample auxiliary power data:")
                for col in aux_cols:
                    print(f"    {col}: {assumptions_df[col].dropna().head(3).tolist()}")
        
        # 3. Check current state calculations.xlsx for PLF formulas
        print(f"\n📊 ANALYZING CURRENT STATE CALCULATIONS.XLSX:")
        print("-" * 40)
        
        calc_file = '/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH-feat-agi-tech-int-007/current state calculations.xlsx'
        calc_xl = pd.ExcelFile(calc_file)
        calc_sheets = calc_xl.sheet_names
        print(f"  • Calculation sheets: {calc_sheets}")
        
        # Check Generation & PLF sheet
        if 'Generation & PLF' in calc_sheets:
            plf_df = pd.read_excel(calc_file, sheet_name='Generation & PLF')
            print(f"  • Generation & PLF columns: {list(plf_df.columns)}")
            
            # Look for Case 1 and Case 4 formulas
            case_cols = [col for col in plf_df.columns if 'case' in col.lower()]
            print(f"  • Case columns: {case_cols}")
            
            if not plf_df.empty:
                print(f"  • Sample PLF formulas:")
                for idx, row in plf_df.head(5).iterrows():
                    for col in case_cols:
                        if pd.notna(row[col]):
                            print(f"    {col}: {row[col]}")
        
        # 4. Test data extraction for Antelope Valley Station
        print(f"\n📊 TESTING DATA EXTRACTION FOR ANTELOPE VALLEY:")
        print("-" * 50)
        
        # Try to extract generation and fuel consumption data for Antelope Valley
        antelope_data_summary = {}
        
        for sheet_name in coal_sheets:
            if sheet_name in sheet_names:
                df = pd.read_excel(excel_file, sheet_name=sheet_name)
                antelope_data = df[df['Plant Name'].str.contains('Antelope Valley', na=False, case=False)]
                
                if not antelope_data.empty:
                    year = sheet_name.split(' ')[1]
                    antelope_data_summary[year] = {}
                    
                    # Extract generation data
                    generation_cols = [col for col in df.columns if 'generation' in col.lower() and 'megawatt' in col.lower()]
                    fuel_cols = [col for col in df.columns if 'fuel' in col.lower() and 'consumption' in col.lower()]
                    
                    for idx, row in antelope_data.iterrows():
                        unit_id = row.get('Unit ID', 'Unknown')
                        unit_data = {}
                        
                        # Extract generation data
                        for col in generation_cols:
                            if pd.notna(row[col]):
                                unit_data[f'generation_{col}'] = row[col]
                        
                        # Extract fuel consumption data
                        for col in fuel_cols:
                            if pd.notna(row[col]):
                                unit_data[f'fuel_{col}'] = row[col]
                        
                        if unit_data:
                            antelope_data_summary[year][f'Unit_{unit_id}'] = unit_data
        
        # Display extracted data
        if antelope_data_summary:
            print("✅ EXTRACTED ANTELOPE VALLEY DATA:")
            for year, year_data in antelope_data_summary.items():
                print(f"  📅 {year}:")
                for unit, unit_data in year_data.items():
                    print(f"    🔧 {unit}:")
                    for field, value in unit_data.items():
                        print(f"      • {field}: {value}")
        else:
            print("❌ No Antelope Valley data found in coal sheets")
        
        # 5. Provide implementation recommendations
        print(f"\n🎯 IMPLEMENTATION RECOMMENDATIONS:")
        print("-" * 40)
        print("1. ✅ Use 'Net Generation (Megawatthours)' from Coal yearly sheets")
        print("2. ✅ Use 'Electric Fuel Consumption Quantity' from Coal yearly sheets")
        print("3. ✅ Apply PLF Case 1 (plant level) and Case 4 (unit level) formulas")
        print("4. ✅ Use auxiliary power values from Assumptions sheet")
        print("5. ✅ Calculate gross generation = net generation / (1 - auxiliary_power_percent)")
        
    except Exception as e:
        print(f"❌ Error analyzing USA Details structure: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    analyze_usa_details_structure()
