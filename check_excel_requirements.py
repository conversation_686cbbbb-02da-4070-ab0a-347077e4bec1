#!/usr/bin/env python3
"""
Check what variables are required for PLF and Generation calculations from Excel
"""

import pandas as pd
import sys

try:
    # Read the Excel file
    excel_file = '/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH-feat-agi-tech-int-007/current state calculations.xlsx'
    
    print("🔍 ANALYZING EXCEL FILE FOR REQUIRED VARIABLES")
    print("=" * 60)
    
    # Read all sheets
    xl = pd.ExcelFile(excel_file)
    print(f"📊 Available sheets: {xl.sheet_names}")
    
    print("\n" + "="*60)
    print("📋 GENERATION & PLF SHEET ANALYSIS")
    print("="*60)
    
    # Read Generation & PLF sheet
    gen_plf_df = pd.read_excel(excel_file, sheet_name='Generation & PLF')
    
    print("📊 Generation & PLF Sheet Contents:")
    print("-" * 40)
    print(gen_plf_df.to_string())
    
    print("\n" + "="*60)
    print("📋 HEAT RATE & EFFICIENCY SHEET ANALYSIS")
    print("="*60)

    # Read Heat Rate & Efficiency sheet
    heat_eff_df = pd.read_excel(excel_file, sheet_name='Heat Rate & Efficiency')

    print("📊 Heat Rate & Efficiency Sheet Contents:")
    print("-" * 40)
    print(heat_eff_df.to_string())

    print("\n" + "="*60)
    print("📋 FUEL CONSUMPTION & EMISSIONS SHEET ANALYSIS")
    print("="*60)

    # Read Fuel Consumption & Emissions sheet
    fuel_emissions_df = pd.read_excel(excel_file, sheet_name='Fuel Consumption & Emissions')

    print("📊 Fuel Consumption & Emissions Sheet Contents:")
    print("-" * 40)
    print(fuel_emissions_df.to_string())

    print("\n" + "="*60)
    print("🔍 REQUIRED VARIABLES FOR PLF CALCULATIONS")
    print("="*60)
    
    # Analyze the formulas and identify required variables
    print("Based on the Excel formulas, the required variables are:")
    print()
    
    print("📈 FOR PLF CALCULATIONS:")
    print("  1. PLF Case 1 (Plant to Unit):")
    print("     - plant_generation_mwh (Annual generation at plant level)")
    print("     - plant_capacity_mw (Total plant capacity)")
    print("     - unit_capacity_mw (Individual unit capacity)")
    print("     - paf_plant (Plant Availability Factor - optional, default 1.0)")
    print("     - paf_unit (Unit Availability Factor - optional, default 1.0)")
    print()
    
    print("  2. PLF Case 2 (Direct Unit):")
    print("     - unit_generation_mwh (Annual generation at unit level)")
    print("     - unit_capacity_mw (Unit capacity)")
    print("     - paf_unit (Unit Availability Factor - optional, default 1.0)")
    print()
    
    print("  3. PLF Case 3 (From Fuel Consumption):")
    print("     - fuel_consumed_tons (Annual fuel consumption)")
    print("     - gcv_coal (Gross Calorific Value of coal)")
    print("     - efficiency (Plant efficiency)")
    print("     - unit_capacity_mw (Unit capacity)")
    print()
    
    print("  4. PLF Case 4 (From Emissions):")
    print("     - annual_emission_mt (Annual emissions in MtCO2)")
    print("     - coal_emission_factor (Emission factor of coal)")
    print("     - gcv_coal (Gross Calorific Value of coal)")
    print("     - efficiency (Plant efficiency)")
    print("     - unit_capacity_mw (Unit capacity)")
    print()
    
    print("🔋 FOR AUXILIARY POWER CALCULATIONS:")
    print("     - unit_capacity_mw (Unit capacity)")
    print("     - technology (subcritical/supercritical/ultra_supercritical)")
    print()
    
    print("⚡ FOR GROSS POWER GENERATION CALCULATIONS:")
    print("     - plf (Plant Load Factor)")
    print("     - unit_capacity_mw (Unit capacity)")
    print("     - auxiliary_power_percent (Auxiliary power consumption %)")
    print()
    
    print("🔥 FOR HEAT RATE CALCULATIONS:")
    print("     - efficiency (Plant efficiency)")
    print("     OR")
    print("     - fuel_consumed_tons + generation_mwh + gcv_coal")
    print()

    print("\n" + "="*60)
    print("📊 DETAILED ANALYSIS OF ALL EXCEL FORMULAS")
    print("="*60)

    print("🔥 HEAT RATE & EFFICIENCY FORMULAS:")
    print("  1. Heat Rate from Efficiency:")
    print("     - Formula: heat_rate = 860.42 / plant_efficiency")
    print("     - Required: plant_efficiency (decimal, e.g., 0.38 for 38%)")
    print()
    print("  2. Heat Rate from Fuel & Generation:")
    print("     - Formula: heat_rate = (fuel_consumed_tons * gcv_coal * 1000) / generation_mwh")
    print("     - Required: fuel_consumed_tons, gcv_coal, generation_mwh")
    print()
    print("  3. Efficiency from Heat Rate:")
    print("     - Formula: efficiency = 860.42 / heat_rate")
    print("     - Required: heat_rate (kcal/kWh)")
    print()

    print("💨 FUEL CONSUMPTION & EMISSIONS FORMULAS:")
    print("  1. Emission Factor Calculation:")
    print("     - Formula: emission_factor = (fuel_consumed_tons * coal_emission_factor) / generation_mwh")
    print("     - Required: fuel_consumed_tons, coal_emission_factor, generation_mwh")
    print()
    print("  2. Annual Emissions Calculation:")
    print("     - Formula: annual_emissions = generation_mwh * emission_factor")
    print("     - Required: generation_mwh, emission_factor")
    print()
    print("  3. Fuel Consumption from Emissions:")
    print("     - Formula: fuel_consumed = annual_emissions / coal_emission_factor")
    print("     - Required: annual_emissions, coal_emission_factor")
    print()

    print("🔋 AUXILIARY POWER FORMULAS:")
    print("  1. Auxiliary Power Percentage (from matrix):")
    print("     - Based on: technology + capacity_range")
    print("     - Subcritical: 6-10% depending on capacity")
    print("     - Supercritical: 5-8% depending on capacity")
    print("     - Ultra-supercritical: 4-7% depending on capacity")
    print()
    print("  2. Auxiliary Power Consumption (MWh):")
    print("     - Formula: aux_power_mwh = gross_generation_mwh * aux_power_percentage")
    print("     - Required: gross_generation_mwh, aux_power_percentage")
    print()

    print("⚡ GROSS POWER GENERATION FORMULAS:")
    print("  1. From PLF and Capacity:")
    print("     - Formula: gross_generation = unit_capacity_mw * plf * 8760")
    print("     - Required: unit_capacity_mw, plf")
    print()
    print("  2. From Net Generation and Auxiliary Power:")
    print("     - Formula: gross_generation = net_generation / (1 - aux_power_percentage)")
    print("     - Required: net_generation, aux_power_percentage")
    print()
    
    print("\n" + "="*60)
    print("❌ MISSING DATA IN CURRENT JSON")
    print("="*60)
    
    print("The current unit JSON has:")
    print("  ✅ capacity: 477.0 MW")
    print("  ✅ technology: Sub-Critical")
    print("  ✅ coal_unit_efficiency: 0.38")
    print("  ✅ heat_rate: 2200.0 (already calculated)")
    print("  ✅ fuel_type: Lignite")
    print("  ✅ gcv_coal: 6690.0 (WRONG - should be 3350 for Lignite)")
    print()
    print("  ❌ MISSING CRITICAL DATA:")
    print("     - unit_generation_mwh (Annual generation at unit level)")
    print("     - plant_generation_mwh (Annual generation at plant level)")
    print("     - fuel_consumed_tons (Annual fuel consumption)")
    print("     - annual_emission_mt (Annual emissions)")
    print()
    
    print("🔍 CONCLUSION:")
    print("The Excel calculations CANNOT run because the required input data")
    print("(generation_mwh, fuel_consumed_tons, annual_emission_mt) is NOT")
    print("available in the web search results or unit JSON.")
    print()
    print("This is why PLF, auxiliary_power_consumed, and gross_power_generation")
    print("arrays are empty - the calculations cannot proceed without this data!")

    print("\n" + "="*60)
    print("🔍 COMPREHENSIVE VARIABLE REQUIREMENTS")
    print("="*60)

    print("📋 ALL REQUIRED VARIABLES FOR COMPLETE CALCULATIONS:")
    print()
    print("🎯 PRIMARY INPUT VARIABLES (Must be extracted from web search):")
    print("  1. unit_generation_mwh - Annual generation at unit level")
    print("  2. plant_generation_mwh - Annual generation at plant level")
    print("  3. fuel_consumed_tons - Annual fuel consumption in tons")
    print("  4. annual_emission_mt - Annual emissions in million tons CO2")
    print("  5. net_generation_mwh - Net generation (if gross not available)")
    print("  6. coal_emission_factor - Emission factor for coal (kg CO2/kg coal)")
    print("  7. plant_efficiency - Overall plant efficiency (decimal)")
    print("  8. unit_efficiency - Individual unit efficiency (decimal)")
    print()
    print("🔧 SECONDARY VARIABLES (Available or calculable):")
    print("  1. unit_capacity_mw - Unit capacity (available)")
    print("  2. plant_capacity_mw - Total plant capacity (sum of units)")
    print("  3. technology - Technology type (available)")
    print("  4. gcv_coal - Gross calorific value (lookup by coal type)")
    print("  5. paf_plant/paf_unit - Plant/Unit availability factor (default 1.0)")
    print("  6. heat_rate - Can be calculated from efficiency")
    print()
    print("📊 TIME SERIES VARIABLES (Need year-wise data 2020-2024):")
    print("  1. generation_mwh (by year)")
    print("  2. fuel_consumed_tons (by year)")
    print("  3. annual_emission_mt (by year)")
    print("  4. emission_factor (by year)")
    print("  5. plf (by year)")
    print("  6. auxiliary_power_consumed (by year)")
    print("  7. gross_power_generation (by year)")

except Exception as e:
    print(f"❌ Error reading Excel file: {e}")
    import traceback
    traceback.print_exc()
