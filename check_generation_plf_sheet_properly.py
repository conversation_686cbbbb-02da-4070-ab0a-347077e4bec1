#!/usr/bin/env python3
"""
Properly check the Generation & PLF sheet to understand the exact formulas and methods
"""

import pandas as pd

def check_generation_plf_sheet():
    """Check the Generation & PLF sheet properly"""
    
    print("🔍 PROPERLY CHECKING GENERATION & PLF SHEET")
    print("=" * 60)
    
    excel_file = '/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH-feat-agi-tech-int-007/current state calculations.xlsx'
    
    try:
        # Read the Generation & PLF sheet specifically
        df = pd.read_excel(excel_file, sheet_name='Generation & PLF')
        
        print(f"📊 Sheet Shape: {df.shape}")
        print(f"📋 Columns: {list(df.columns)}")
        print()
        
        print("📄 COMPLETE GENERATION & PLF SHEET CONTENT:")
        print("-" * 50)
        
        # Show all rows to understand the structure
        for idx, row in df.iterrows():
            print(f"Row {idx}:")
            for col in df.columns:
                if pd.notna(row[col]):
                    print(f"  {col}: {row[col]}")
            print()
        
        print("\n🔍 LOOKING FOR PLF CALCULATION METHODS:")
        print("-" * 40)
        
        # Look for specific PLF formulas and cases
        plf_cases = []
        generation_methods = []
        
        for idx, row in df.iterrows():
            for col in df.columns:
                cell_value = str(row[col]).strip()
                if pd.notna(row[col]) and cell_value != 'nan':
                    # Look for PLF formulas
                    if 'PLF' in cell_value and '=' in cell_value:
                        plf_cases.append(f"Row {idx}, {col}: {cell_value}")
                    
                    # Look for generation formulas
                    if 'Generation' in cell_value and '=' in cell_value:
                        generation_methods.append(f"Row {idx}, {col}: {cell_value}")
                    
                    # Look for case descriptions
                    if 'Case' in cell_value:
                        print(f"📋 Found Case: Row {idx}, {col}: {cell_value}")
        
        print(f"\n🧮 PLF FORMULAS FOUND:")
        for formula in plf_cases:
            print(f"  ✅ {formula}")
        
        print(f"\n🔧 GENERATION FORMULAS FOUND:")
        for formula in generation_methods:
            print(f"  ✅ {formula}")
        
        print(f"\n🎯 KEY QUESTIONS TO ANSWER:")
        print("-" * 30)
        print("1. What are the exact PLF calculation cases?")
        print("2. Which case should be used for USA plants?")
        print("3. What variables are needed for each case?")
        print("4. Where do we get the input data for each variable?")
        print("5. Is the 'Max Generation = Capacity × 8760 × PAF' formula actually in the sheet?")
        
    except Exception as e:
        print(f"❌ Error reading sheet: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_generation_plf_sheet()
    
    print("\n" + "=" * 60)
    print("🎯 NEXT STEPS")
    print("=" * 60)
    print()
    print("1. 🔍 Identify the exact PLF cases and their formulas")
    print("2. 🔍 Understand which variables are required for each case")
    print("3. 🔍 Map the available USA data to the required variables")
    print("4. 🔍 Implement ONLY the formulas that are explicitly in the sheet")
    print("5. 🔍 Don't assume any formulas that aren't clearly documented")
