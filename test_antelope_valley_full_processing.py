#!/usr/bin/env python3
"""
Test full processing of Antelope Valley Station to create fresh JSON files
"""

import sys
import os
import json
sys.path.append('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH-feat-agi-tech-int-007')
sys.path.append('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH-feat-agi-tech-int-007/backend/src')

from excel_power_plant_tool import ExcelPowerPlantTool
from agent.unit_extraction_stages import combine_unit_data

def test_antelope_valley_full_processing():
    """Test full processing and create fresh JSON files"""
    
    print("🔧 TESTING FULL ANTELOPE VALLEY PROCESSING")
    print("=" * 70)
    print("Creating fresh JSON files to verify all fixes...")
    print()
    
    try:
        excel_tool = ExcelPowerPlantTool('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH-feat-agi-tech-int-007-feat-agi-tech-int-007/USA Details.xlsx')
        plant_name = "Antelope Valley"
        
        # Process both units
        for unit_num in ['1', '2']:
            print(f"🔧 PROCESSING UNIT {unit_num}:")
            print("-" * 40)
            
            # Get Excel data
            excel_results = excel_tool.get_plant_data(plant_name, unit_num)
            
            if excel_results:
                excel_data = excel_results[0]
                
                # Create plant context
                plant_context = {
                    'excel_data': excel_data,
                    'plant_name': plant_name,
                    'country': 'United States',
                    'plant_uid': f'test-antelope-full-{unit_num}',
                    'plant_type': 'coal'
                }
                
                # Process unit data
                combined_data = combine_unit_data([], unit_num, plant_context)
                
                # Extract key data
                gcv_coal = combined_data.get('gcv_coal')
                heat_rate = combined_data.get('heat_rate')
                efficiency = combined_data.get('coal_unit_efficiency')
                plf = combined_data.get('plf', [])
                auxiliary_power = combined_data.get('auxiliary_power_consumed', [])
                gross_power = combined_data.get('gross_power_generation', [])
                fuel_type = combined_data.get('fuel_type', [])
                
                print(f"  📊 PROCESSED DATA:")
                print(f"    GCV Coal: {gcv_coal} kcal/kg")
                print(f"    Heat Rate: {heat_rate} kcal/kWh")
                print(f"    Efficiency: {efficiency}%")
                print(f"    PLF records: {len(plf)}")
                print(f"    Auxiliary power records: {len(auxiliary_power)}")
                print(f"    Gross power records: {len(gross_power)}")
                
                if fuel_type:
                    coal_type = fuel_type[0].get('type', 'Unknown')
                    print(f"    Coal type: {coal_type}")
                
                # Save to JSON file for inspection
                output_filename = f"antelope_valley_unit_{unit_num}_test.json"
                
                try:
                    with open(output_filename, 'w') as f:
                        json.dump(combined_data, f, indent=2, ensure_ascii=False)
                    print(f"  💾 Saved to: {output_filename}")
                    
                    # Verify the saved file
                    with open(output_filename, 'r') as f:
                        saved_data = json.load(f)
                    
                    saved_plf = saved_data.get('plf', [])
                    saved_aux = saved_data.get('auxiliary_power_consumed', [])
                    saved_gross = saved_data.get('gross_power_generation', [])
                    saved_gcv = saved_data.get('gcv_coal')
                    
                    print(f"  🔍 VERIFICATION FROM SAVED FILE:")
                    print(f"    PLF in file: {len(saved_plf)} records")
                    print(f"    Auxiliary power in file: {len(saved_aux)} records")
                    print(f"    Gross power in file: {len(saved_gross)} records")
                    print(f"    GCV in file: {saved_gcv} kcal/kg")
                    
                    # Check if arrays are empty
                    arrays_empty = (len(saved_plf) == 0 and len(saved_aux) == 0 and len(saved_gross) == 0)
                    
                    if arrays_empty:
                        print(f"  ❌ ARRAYS ARE EMPTY IN SAVED FILE - ISSUE CONFIRMED")
                    else:
                        print(f"  ✅ ARRAYS ARE POPULATED IN SAVED FILE")
                    
                    # Check GCV
                    if fuel_type:
                        coal_type = fuel_type[0].get('type', 'Unknown')
                        expected_gcv = {'Lignite': 3350, 'Bituminous': 6690, 'Sub-bituminous': 4900}
                        expected = expected_gcv.get(coal_type, 6690)
                        
                        if saved_gcv == expected:
                            print(f"  ✅ GCV IS CORRECT IN SAVED FILE")
                        else:
                            print(f"  ❌ GCV IS WRONG IN SAVED FILE: {saved_gcv} (expected {expected})")
                    
                except Exception as e:
                    print(f"  ❌ Failed to save/verify file: {e}")
                
                print()
            
            else:
                print(f"  ❌ No Excel data found for Unit {unit_num}")
                print()
        
        # List all Antelope Valley JSON files created
        print("📁 CREATED FILES:")
        print("-" * 30)
        for filename in os.listdir('.'):
            if 'antelope_valley' in filename.lower() and filename.endswith('.json'):
                print(f"  📄 {filename}")
        
        print()
        print("🔍 SUMMARY:")
        print("- Created fresh JSON files for both units")
        print("- Verified GCV fix is working")
        print("- Checked if arrays are properly saved")
        print("- You can now compare these test files with your bucket JSON")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_antelope_valley_full_processing()
