
import requests
from bs4 import BeautifulSoup
import os
import certifi
from urllib.parse import urljoin, urlparse
import mediapipe as mp
import cv2
import easyocr

# Handle googlesearch import gracefully
try:
    from googlesearch import search
except ImportError:
    print("⚠️ googlesearch-python not installed. Image search will be limited.")
    def search(query, num_results=10):
        """Fallback search function when googlesearch is not available"""
        print(f"⚠️ Search function called but googlesearch not available for query: {query}")
        return []
import boto3
from dotenv import load_dotenv
import logging
import mimetypes
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
from PIL import Image
import shutil

load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

# ========== CONFIG ==========
# Use S3-specific credentials for image storage
AWS_ACCESS_KEY = os.getenv('S3_AWS_ACCESS_KEY_ID')
AWS_SECRET_KEY = os.getenv('S3_AWS_SECRET_ACCESS_KEY')
AWS_REGION = os.getenv('AWS_REGION', 'ap-south-1')
S3_BUCKET = 'clem-transition-plant'  # Use the correct S3 bucket
SCRAPER_API_KEY = os.getenv('SCRAPER_API_KEY')

# Debug logging for S3 configuration
print(f"🔧 Image Extraction S3 Configuration:")
print(f"   - S3 Access Key: {AWS_ACCESS_KEY[:8] + '...' if AWS_ACCESS_KEY else 'NOT SET'}")
print(f"   - S3 Region: {AWS_REGION}")
print(f"   - S3 Bucket: {S3_BUCKET}")
# ============================

def search_google(query: str, page: int, num_results=5):
    """Search Google using ScraperAPI"""
    payload = {'api_key': SCRAPER_API_KEY, 'query': query, 'page': page, 'num': num_results}
    try:
        response = requests.get('https://api.scraperapi.com/structured/google/search', params=payload, verify=certifi.where())
        response.raise_for_status()
        results = response.json()
        links = []
        for result in results.get('organic_results', []):
            link = result.get('link', '')
            if link:
                links.append(link)
        return links
    except Exception as e:
        print(f"Error during Google search: {e}")
        return []

def is_google_link(url):
    """Return True if the URL is a Google-related domain."""
    parsed = urlparse(url)
    domain = parsed.netloc.lower()
    return "google." in domain or "webcache." in domain


def test_link(url):
    """Check if a URL is valid, complete (with scheme and netloc), and not a Google link."""
    try:
        parsed = urlparse(url)
        if parsed.scheme not in ('http', 'https'):
            return False
        if not parsed.netloc:
            return False
        if is_google_link(url):
            return False
        return True
    except Exception:
        return False

def get_valid_links(name, max_results=3):

    """Search for the power plant and return up to 3 valid links."""
    keywords = ["power", "plant", "station"]

    if any(keyword in name.lower() for keyword in keywords):
        query = f"{name} official site"
    else:
        query = f"{name} power station/plant official site"

    valid_links = []

    try:
        results = search(query)  # Get more to filter later

        for url in results:
            if test_link(url):
                valid_links.append(url)
                if len(valid_links) >= max_results:
                    break

        if not valid_links:
            print("No valid links found.")
            return None
        return valid_links

    except Exception as e:
        logging.error(f"Error during search for '{query}': {e}")
        return None
def get_retry_session():
    session = requests.Session()
    retries = Retry(
        total=3,
        backoff_factor=1,
        status_forcelist=[429, 500, 502, 503, 504],
        raise_on_status=False
    )
    adapter = HTTPAdapter(max_retries=retries)
    session.mount('http://', adapter)
    session.mount('https://', adapter)
    return session

# Function to scrape and download images
def get_images(base_url, LINKS, image_folder):
    if not LINKS:
        logger.warning("⚠️ No links found in the list.")
        return

    if not os.path.exists(image_folder):
        os.makedirs(image_folder)

    session = get_retry_session()
    downloaded_images = set()

    for count, link in enumerate(LINKS, start=1):
        try:
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
                              "AppleWebKit/537.36 (KHTML, like Gecko) "
                              "Chrome/115.0.0.0 Safari/537.36"
            }
            response = session.get(link, headers=headers, timeout=10)
            if response.status_code != 200:
                logger.error(f"❌ Failed to retrieve page: {link} (Status {response.status_code})")
                continue
        except Exception as e:
            logger.error(f"❌ Failed to request page {link}: {e}")
            continue

        soup = BeautifulSoup(response.text, "html.parser")
        img_tags = soup.find_all('img')
        logger.info(f"\n🔗 Scraping Link {count}: {link}")
        logger.info(f"🖼️ Found {len(img_tags)} images")

        for img in img_tags:
            img_url = img.get('src')
            if not img_url:
                continue

            # Clean and construct proper URL
            clean_img_url = img_url.split('?')[0].split('#')[0]

            # Handle different URL formats
            if clean_img_url.startswith('http'):
                img_url = clean_img_url  # Already absolute URL
            elif clean_img_url.startswith('//'):
                img_url = f"https:{clean_img_url}"  # Protocol-relative URL
            else:
                img_url = urljoin(base_url, clean_img_url)  # Relative URL

            if img_url in downloaded_images:
                logger.info(f"🔁 Skipping duplicate: {img_url}")
                continue

            if img_url.lower().endswith('.svg'):
                logger.info(f"🚫 Skipping SVG (by extension): {img_url}")
                continue

            # Quick URL validation before attempting download
            try:
                head_resp = session.head(img_url, headers=headers, timeout=5)
                if head_resp.status_code == 404:
                    logger.info(f"🚫 Skipping 404 URL: {img_url}")
                    continue
                elif head_resp.status_code >= 400:
                    logger.info(f"🚫 Skipping invalid URL (status {head_resp.status_code}): {img_url}")
                    continue
            except Exception as e:
                logger.info(f"🚫 Skipping URL validation failed: {img_url} - {e}")
                continue

            # Retry download for image
            for attempt in range(3):
                try:
                    img_resp = session.get(img_url, headers=headers, timeout=10)
                    img_resp.raise_for_status()

                    content_type = img_resp.headers.get('Content-Type', '').split(';')[0].strip()
                    if content_type == 'image/svg+xml':
                        logger.info(f"🚫 Skipping SVG (by content-type): {img_url}")
                        break

                    # Determine filename
                    filename = os.path.basename(urlparse(img_url).path)
                    if not filename or '.' not in filename:
                        extension = mimetypes.guess_extension(content_type) or '.jpg'
                        filename = f"image_{count}_{len(downloaded_images)}{extension}"

                    filepath = os.path.join(image_folder, filename)

                    # Avoid filename collision
                    base, ext = os.path.splitext(filename)
                    i = 1
                    while os.path.exists(filepath):
                        filename = f"{base}_{i}{ext}"
                        filepath = os.path.join(image_folder, filename)
                        i += 1

                    with open(filepath, 'wb') as f:
                        f.write(img_resp.content)

                    downloaded_images.add(img_url)
                    logger.info(f"✅ Saved: {filename}")
                    break  # success, break retry loop

                except Exception as e:
                    # Less noisy logging for common errors
                    if "404" in str(e) or "Not Found" in str(e):
                        logger.info(f"🚫 Image not found (404): {img_url}")
                        break  # Don't retry 404 errors
                    else:
                        logger.warning(f"⚠️ Attempt {attempt + 1} failed for {img_url}: {e}")
                        if attempt == 2:
                            logger.warning(f"⚠️ Failed to download after 3 attempts: {img_url}")



def image_scaper(name, urls,image_folder):
    

    for url in urls:
        if not test_link(url):  # Optional: add link validation
            print(f"⚠️ Skipping invalid URL: {url}")
            continue

        parsed_url = urlparse(url)
        base_url = f"{parsed_url.scheme}://{parsed_url.netloc}"
        
        # Pass a list containing the original URL, not the parsed object
        get_images(base_url, [url], image_folder)

    print(f"✅ Image download completed for {name}")


def face_remove(images_folder):
    image_files=os.listdir(images_folder)

    print(len(image_files))
    for i in image_files:
        print(i)

    mp_face = mp.solutions.face_detection
    mp_drawing = mp.solutions.drawing_utils

    with mp_face.FaceDetection(model_selection=1, min_detection_confidence=0.5) as face_detection:
        for image_link in image_files:
            image_path = os.path.join(images_folder, image_link)
            img = cv2.imread(image_path)
            image_valid=True
            if img is None:
                image_valid=False
                print("Something wrong with the image")
            else:
                if image_valid:
                    img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
                    results = face_detection.process(img_rgb)

                    if not results.detections:
                        print(f"⚠️ No face found in {image_path}")
                    else:
                        print(f"✅ {len(results.detections)} face(s) detected in {image_path}")
                        for detection in results.detections:
                            mp_drawing.draw_detection(img, detection)

                        print("Deleting the image")
                        os.remove(image_path) 


def text_remove(images_folder, confidence_threshold=0.5):
    reader = easyocr.Reader(['en'])
    image_files = os.listdir(images_folder)

    for image_link in image_files:
        image_path = os.path.join(images_folder, image_link)
        img = cv2.imread(image_path)

        if img is None:
            print(f"❌ Unable to load image: {image_path}")
            continue

        results = reader.readtext(image_path)

        if not results:
            print(f"✅ No text found in: {image_path}")
        else:
            high_conf_texts = [
                (text, conf) for _, text, conf in results if conf >= confidence_threshold
            ]

            if high_conf_texts:
                print(f"📄 Text found in: {image_path}")
                for text, conf in high_conf_texts:
                    print(f"📝 '{text}' (Confidence: {conf:.2f})")
                print(f"🗑️ Deleting image with high-confidence text: {image_path}")
                os.remove(image_path)
            else:
                print(f"🧐 Only low-confidence text in: {image_path} — keeping image.")




def remove_small_images(folder_path):
    """
    Removes images in the given folder with width or height less than 200 pixels.
    
    Args:
        folder_path (str): Path to the folder containing images.
    """
    if not os.path.isdir(folder_path):
        print(f"Error: {folder_path} is not a valid directory.")
        return

    for filename in os.listdir(folder_path):
        file_path = os.path.join(folder_path, filename)

        if os.path.isfile(file_path):
            try:
                with Image.open(file_path) as img:
                    width, height = img.size
                    if width < 150 or height < 150:
                        os.remove(file_path)
                        print(f"Removed: {filename} ({width}x{height})")
            except Exception as e:
                print(f"Skipping {filename}: {e}")

import os

def remove_non_image_files(folder_path, allowed_extensions=None):
    """
    Removes all files from the folder that do not have image extensions.

    Args:
        folder_path (str): Path to the folder to scan.
        allowed_extensions (set or list): Optional custom set of allowed image extensions.
    """
    if not os.path.isdir(folder_path):
        print(f"Error: {folder_path} is not a valid directory.")
        return

    # Default allowed image extensions (case-insensitive)
    if allowed_extensions is None:
        allowed_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp'}

    allowed_extensions = {ext.lower() for ext in allowed_extensions}

    for filename in os.listdir(folder_path):
        file_path = os.path.join(folder_path, filename)

        if os.path.isfile(file_path):
            _, ext = os.path.splitext(filename)
            if ext.lower() not in allowed_extensions:
                try:
                    os.remove(file_path)
                    print(f"Removed non-image file: {filename}")
                except Exception as e:
                    print(f"Error removing {filename}: {e}")


def total_filter(image_folder):
    remove_non_image_files(image_folder)
    print("🧹 Non-image files removed ✅")
    
    remove_small_images(image_folder)
    print("📏 Small images (less than 200px) removed ✅")
    
    text_remove(image_folder)
    print("📝 Images with text removed ✅")
    
    face_remove(image_folder)
    print("👤 Images with faces removed ✅")


def upload_folder_to_s3(local_folder, bucket_name, aws_access_key, aws_secret_key, aws_region="us-east-1", s3_prefix=None, max_images=5):
    """Upload files from a local folder to S3 bucket with a maximum limit."""
    s3 = boto3.client(
        's3',
        aws_access_key_id=aws_access_key,
        aws_secret_access_key=aws_secret_key,
        region_name=aws_region
    )
    s3_links = []
    uploaded_count = 0
    
    # Collect all files first
    all_files = []
    for root, _, files in os.walk(local_folder):
        for file in files:
            local_path = os.path.join(root, file)
            all_files.append(local_path)
    
    # Sort files to ensure consistent selection
    all_files.sort()
    
    # Upload only up to max_images
    for local_path in all_files:
        if uploaded_count >= max_images:
            logger.info(f"⚠️ Reached maximum limit of {max_images} images. Skipping remaining {len(all_files) - uploaded_count} images.")
            break
            
        relative_path = os.path.relpath(local_path, local_folder)
        s3_key = os.path.join(s3_prefix, relative_path) if s3_prefix else relative_path
        s3_key = s3_key.replace("\\", "/")  # Windows fix
        
        try:
            # Determine MIME type for proper browser rendering
            import mimetypes
            import pathlib
 
            mime_type, _ = mimetypes.guess_type(pathlib.Path(local_path).name)
            mime_type = mime_type or "image/jpeg"  # Safe default
 
            # Upload with proper ContentType to ensure browser renders instead of downloads
            s3.upload_file(
                local_path,
                bucket_name,
                s3_key,
                ExtraArgs={
                    "ContentType": mime_type,
                    # ContentDisposition defaults to "inline" (renders in browser)
                }
            )
            s3_url = f"https://{bucket_name}.s3.amazonaws.com/{s3_key}"
            s3_links.append(s3_url)
            uploaded_count += 1
            logger.info(f"✅ Uploaded ({uploaded_count}/{max_images}): {os.path.basename(local_path)} (ContentType: {mime_type})")
        except Exception as e:
            logger.error(f"❌ Failed to upload {os.path.basename(local_path)}: {e}")
    
    logger.info(f"🎉 Successfully uploaded {uploaded_count} out of {len(all_files)} total images to S3")
    return s3_links

def extract_and_upload_images(powerplant: str, session_id: str = "unknown") -> list:
    """
    Extract images for a power plant and upload to S3 using the new logic.
    
    Args:
        powerplant (str): Name of the power plant
        session_id (str): Session ID for logging
        
    Returns:
        list: List of S3 URLs for uploaded images
    """
    logger.info(f"[Session {session_id}] 🖼️ Starting image extraction for: {powerplant}")
    
    try:
        # Create image folder
        image_folder = f"{powerplant.replace(' ', '_')}_images_{session_id}"
        os.makedirs(image_folder, exist_ok=True)
        
        # Get valid links using the new search logic
        logger.info(f"[Session {session_id}] 🔗 Fetching image URLs...")
        urls = get_valid_links(powerplant, max_results=3)
        
        if not urls:
            logger.warning(f"[Session {session_id}] ❌ No valid URLs found for {powerplant}")
            return []
        
        logger.info(f"[Session {session_id}] ✅ Found {len(urls)} URLs for '{powerplant}'")
        
        # Start image scraping using the new logic
        logger.info(f"[Session {session_id}] 📥 Starting image scraping...")
        image_scaper(powerplant, urls, image_folder)
        logger.info(f"[Session {session_id}] ✅ Image scraping completed")
        
        # Apply comprehensive filtering
        if os.path.exists(image_folder):
            logger.info(f"[Session {session_id}] 🧹 Starting filtering process...")
            total_filter(image_folder)
            logger.info(f"[Session {session_id}] ✅ Filtering completed")
        
        # Upload to S3 and get URLs (maximum 5 images)
        s3_links = upload_folder_to_s3(
            local_folder=image_folder,
            bucket_name=S3_BUCKET,
            aws_access_key=AWS_ACCESS_KEY,
            aws_secret_key=AWS_SECRET_KEY,
            aws_region=AWS_REGION,
            s3_prefix=f"{image_folder}_{session_id}",
            max_images=5
        )

        logger.info(f"[Session {session_id}] ✅ Successfully uploaded {len(s3_links)} images for {powerplant}")
        
        # Clean up local folder after upload
        try:
            if os.path.exists(image_folder):
                shutil.rmtree(image_folder)
                logger.info(f"[Session {session_id}] 🧹 Cleaned up local image folder")
        except Exception as e:
            logger.warning(f"[Session {session_id}] ⚠️ Could not clean up folder: {e}")
        
        return s3_links
        
    except Exception as e:
        logger.error(f"[Session {session_id}] ❌ Error in image extraction: {e}")
        return []

def main():
    """CLI interface for standalone usage"""
    powerplant = input("Enter the power plant name: ").strip()
    s3_links = extract_and_upload_images(powerplant, "cli")
    
    print(f"\n🎉 Process completed!")
    print(f"Uploaded {len(s3_links)} images. S3 links:")
    for i, url in enumerate(s3_links, 1):
        print(f"{i}. {url}")
    return s3_links

if __name__ == "__main__":
    main()