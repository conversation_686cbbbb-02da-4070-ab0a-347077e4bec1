"""
Heat Rate and Efficiency Calculator
Implements formulas from current state calculations.xlsx
"""

import logging
from typing import Dict, Optional, Tuple

class HeatRateEfficiencyCalculator:
    """Calculator for heat rate and efficiency based on Excel formulas"""
    
    # Constants from Assumptions sheet
    CONVERSION_CONSTANT = 860.42  # 1 kWh = 860.42 kcal
    
    # GCV Values (kcal/kg) - Simple hardcoded values as requested
    GCV_VALUES = {
        'bituminous': 6690,
        'sub bituminous': 4900,
        'sub-bituminous': 4900,
        'lignite': 3350,
        'anthracite': 6690,  # Using bituminous as fallback
        'refined coal': 6690,  # Using bituminous as fallback
    }
    
    # Technology efficiency defaults from Assumptions sheet
    TECHNOLOGY_EFFICIENCY = {
        'subcritical': 0.36,
        'supercritical': 0.395,
        'ultra-supercritical': 0.4575,
        'advanced_usc': 0.48,
    }
    
    def __init__(self, session_id: str = ""):
        self.session_id = session_id
        self.logger = logging.getLogger(__name__)



    def get_gcv_for_coal_type(self, coal_type: str) -> float:
        """
        Get GCV value for coal type
        
        Args:
            coal_type: Type of coal (bituminous, sub-bituminous, lignite, anthracite)
            
        Returns:
            GCV value in kCal/kg
        """
        if not coal_type:
            self.logger.warning(f"[Session {self.session_id}] No coal type specified, using bituminous default")
            return self.GCV_VALUES['bituminous']
        
        # Normalize coal type
        coal_type_lower = coal_type.lower().strip()
        
        # 🚨 CRITICAL FIX: Handle variations in naming - CHECK SUB-BITUMINOUS FIRST!
        if ('sub-bituminous' in coal_type_lower or
            'subbituminous' in coal_type_lower or
            'sub bituminous' in coal_type_lower):  # Handle space version too!
            return self.GCV_VALUES['sub-bituminous']
        elif 'bituminous' in coal_type_lower:  # Check this AFTER sub-bituminous
            return self.GCV_VALUES['bituminous']
        elif 'lignite' in coal_type_lower:
            return self.GCV_VALUES['lignite']
        elif 'anthracite' in coal_type_lower:
            return self.GCV_VALUES['anthracite']
        else:
            self.logger.warning(f"[Session {self.session_id}] Unknown coal type '{coal_type}', using bituminous default")
            return self.GCV_VALUES['bituminous']
    
    def calculate_heat_rate_from_efficiency(self, efficiency: float) -> float:
        """
        Case 1.1: Calculate heat rate when only efficiency is known
        Formula: heat_rate = 860.42/plant_efficiency
        
        Args:
            efficiency: Plant efficiency as decimal (e.g., 0.35 for 35%)
            
        Returns:
            Heat rate in kCal/kWh
        """
        if efficiency <= 0:
            raise ValueError(f"Efficiency must be positive, got {efficiency}")
        
        heat_rate = self.CONVERSION_CONSTANT / efficiency
        
        self.logger.info(f"[Session {self.session_id}] Case 1.1 - Heat rate from efficiency:")
        self.logger.info(f"[Session {self.session_id}]   Efficiency: {efficiency}")
        self.logger.info(f"[Session {self.session_id}]   Formula: {self.CONVERSION_CONSTANT} / {efficiency}")
        self.logger.info(f"[Session {self.session_id}]   Heat Rate: {heat_rate:.2f} kCal/kWh")
        
        return heat_rate
    
    def calculate_heat_rate_from_fuel_data(self, fuel_consumed_tons: float, 
                                         gross_generation_mwh: float, 
                                         gcv_kcal_per_kg: float) -> float:
        """
        Case 1.2: Calculate heat rate when generation and fuel consumption are known
        Formula: heat_rate = (fuel_consumed_tons * GCV_kCal_per_kg) / (Gross_Plant_Level_Generation_MWh)
        
        Args:
            fuel_consumed_tons: Total fuel consumption in metric tons
            gross_generation_mwh: Gross generation in MWh
            gcv_kcal_per_kg: Gross Calorific Value in kCal/kg
            
        Returns:
            Heat rate in kCal/kWh
        """
        if fuel_consumed_tons <= 0:
            raise ValueError(f"Fuel consumption must be positive, got {fuel_consumed_tons}")
        if gross_generation_mwh <= 0:
            raise ValueError(f"Gross generation must be positive, got {gross_generation_mwh}")
        
        # Convert MWh to kWh for the calculation
        gross_generation_kwh = gross_generation_mwh * 1000
        
        # Convert tons to kg
        fuel_consumed_kg = fuel_consumed_tons * 1000
        
        heat_rate = (fuel_consumed_kg * gcv_kcal_per_kg) / gross_generation_kwh
        
        self.logger.info(f"[Session {self.session_id}] Case 1.2 - Heat rate from fuel data:")
        self.logger.info(f"[Session {self.session_id}]   Fuel consumed: {fuel_consumed_tons} tons = {fuel_consumed_kg} kg")
        self.logger.info(f"[Session {self.session_id}]   Gross generation: {gross_generation_mwh} MWh = {gross_generation_kwh} kWh")
        self.logger.info(f"[Session {self.session_id}]   GCV: {gcv_kcal_per_kg} kCal/kg")
        self.logger.info(f"[Session {self.session_id}]   Formula: ({fuel_consumed_kg} * {gcv_kcal_per_kg}) / {gross_generation_kwh}")
        self.logger.info(f"[Session {self.session_id}]   Heat Rate: {heat_rate:.2f} kCal/kWh")
        
        return heat_rate
    
    def calculate_efficiency_from_fuel_data(self, gross_generation_mwh: float,
                                          fuel_consumed_tons: float,
                                          gcv_kcal_per_kg: float) -> float:
        """
        Case 2: Calculate efficiency of the plant
        Formula: efficiency = (Gross_Plant_Level_Generation_MWh * 860,420) / (GCV_coal_type_kCal_per_kg * Fuel_Consumed_tons * 1000)
        
        Args:
            gross_generation_mwh: Gross generation in MWh
            fuel_consumed_tons: Fuel consumption in metric tons
            gcv_kcal_per_kg: Gross Calorific Value in kCal/kg
            
        Returns:
            Efficiency as decimal (e.g., 0.35 for 35%)
        """
        if gross_generation_mwh <= 0:
            raise ValueError(f"Gross generation must be positive, got {gross_generation_mwh}")
        if fuel_consumed_tons <= 0:
            raise ValueError(f"Fuel consumption must be positive, got {fuel_consumed_tons}")
        
        # Formula constants
        conversion_factor = 860420  # 860.42 * 1000 for MWh to kWh conversion
        
        numerator = gross_generation_mwh * conversion_factor
        denominator = gcv_kcal_per_kg * fuel_consumed_tons * 1000  # 1000 to convert tons to kg
        
        efficiency = numerator / denominator
        
        self.logger.info(f"[Session {self.session_id}] Case 2 - Efficiency from fuel data:")
        self.logger.info(f"[Session {self.session_id}]   Gross generation: {gross_generation_mwh} MWh")
        self.logger.info(f"[Session {self.session_id}]   Fuel consumed: {fuel_consumed_tons} tons")
        self.logger.info(f"[Session {self.session_id}]   GCV: {gcv_kcal_per_kg} kCal/kg")
        self.logger.info(f"[Session {self.session_id}]   Formula: ({gross_generation_mwh} * {conversion_factor}) / ({gcv_kcal_per_kg} * {fuel_consumed_tons} * 1000)")
        self.logger.info(f"[Session {self.session_id}]   Numerator: {numerator}")
        self.logger.info(f"[Session {self.session_id}]   Denominator: {denominator}")
        self.logger.info(f"[Session {self.session_id}]   Efficiency: {efficiency:.4f} ({efficiency*100:.2f}%)")
        
        return efficiency
    
    def calculate_heat_rate_and_efficiency(self, net_generation_mwh: float,
                                         fuel_consumed_tons: float,
                                         coal_type: str,
                                         auxiliary_power_decimal: float = 0.08) -> Tuple[float, float]:
        """
        Calculate both heat rate and efficiency using available data
        
        Args:
            net_generation_mwh: Net generation in MWh
            fuel_consumed_tons: Total fuel consumed in metric tons
            coal_type: Type of coal used
            auxiliary_power_decimal: Auxiliary power as decimal (default 0.08 for 8%)
            
        Returns:
            Tuple of (heat_rate_kcal_per_kwh, efficiency_decimal)
        """
        self.logger.info(f"[Session {self.session_id}] 🧮 Starting heat rate and efficiency calculations")
        self.logger.info(f"[Session {self.session_id}]   Net generation: {net_generation_mwh} MWh")
        self.logger.info(f"[Session {self.session_id}]   Fuel consumed: {fuel_consumed_tons} tons")
        self.logger.info(f"[Session {self.session_id}]   Coal type: {coal_type}")
        self.logger.info(f"[Session {self.session_id}]   Auxiliary power: {auxiliary_power_decimal} ({auxiliary_power_decimal*100}%)")
        
        # Get GCV for coal type
        gcv_kcal_per_kg = self.get_gcv_for_coal_type(coal_type)
        self.logger.info(f"[Session {self.session_id}]   GCV: {gcv_kcal_per_kg} kCal/kg")
        
        # Calculate gross generation from net generation
        # Gross = Net / (1 - auxiliary_power)
        gross_generation_mwh = net_generation_mwh / (1 - auxiliary_power_decimal)
        self.logger.info(f"[Session {self.session_id}]   Gross generation: {gross_generation_mwh:.2f} MWh")
        
        # Calculate heat rate using Case 1.2 (fuel data method)
        heat_rate = self.calculate_heat_rate_from_fuel_data(
            fuel_consumed_tons, gross_generation_mwh, gcv_kcal_per_kg
        )
        
        # Calculate efficiency using Case 2
        efficiency = self.calculate_efficiency_from_fuel_data(
            gross_generation_mwh, fuel_consumed_tons, gcv_kcal_per_kg
        )
        
        self.logger.info(f"[Session {self.session_id}] ✅ Final Results:")
        self.logger.info(f"[Session {self.session_id}]   Heat Rate: {heat_rate:.2f} kCal/kWh")
        self.logger.info(f"[Session {self.session_id}]   Efficiency: {efficiency:.4f} ({efficiency*100:.2f}%)")
        
        return heat_rate, efficiency
