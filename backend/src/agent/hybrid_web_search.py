"""
Hybrid Web Search System

This module implements an intelligent hybrid web search system that combines:
1. Google Gemini Grounded Generation for simple factual queries
2. Existing sophisticated search system for complex technical queries

The system automatically routes queries based on their complexity and type.
"""

import os
import re
import time
from typing import Dict, List, Any, Optional
from google import genai
from google.genai import types
from agent.prompts import web_searcher_instructions, get_current_date
from agent.configuration import Configuration
from langchain_core.runnables import RunnableConfig


class HybridWebSearch:
    """
    Intelligent hybrid web search system that routes queries between
    grounded generation and sophisticated technical search.
    """
    
    def __init__(self):
        """Initialize the hybrid search system"""
        self.genai_client = genai.Client(api_key=os.getenv("GEMINI_API_KEY"))
        
        # Configure grounded generation
        self.grounding_tool = types.Tool(google_search=types.GoogleSearch())
        self.grounded_config = types.GenerateContentConfig(
            tools=[self.grounding_tool],
            temperature=0,
            max_output_tokens=2048
        )
        
        # Simple factual query patterns
        self.simple_query_patterns = [
            # Ownership queries
            r'.*who.*own.*',
            r'.*owner.*of.*',
            r'.*owned.*by.*',
            r'.*parent.*company.*',
            r'.*operator.*of.*',
            
            # Basic facts
            r'.*capacity.*of.*',
            r'.*location.*of.*',
            r'.*retirement.*date.*',
            r'.*when.*built.*',
            r'.*commissioning.*date.*',
            r'.*country.*of.*',
            r'.*address.*of.*',
            
            # Current status
            r'.*current.*status.*',
            r'.*operational.*status.*',
            r'.*still.*operating.*',
            r'.*shut.*down.*',
            
            # Simple technical facts
            r'.*fuel.*type.*',
            r'.*technology.*type.*',
            r'.*number.*of.*units.*',
        ]
        
        # Complex technical query patterns (use existing system)
        self.complex_query_patterns = [
            # Unit-specific technical data
            r'.*unit.*\d+.*capacity.*',
            r'.*unit.*\d+.*technology.*',
            r'.*unit.*\d+.*efficiency.*',
            r'.*heat.*rate.*',
            r'.*efficiency.*calculation.*',
            
            # GEM Wiki specific searches
            r'.*site:gem\.wiki.*',
            r'.*site:globalenergymonitor\.org.*',
            
            # Technical specifications
            r'.*technical.*specification.*',
            r'.*performance.*data.*',
            r'.*regulatory.*filing.*',
            r'.*annual.*report.*',
            r'.*environmental.*impact.*',
            
            # Grid and PPA details
            r'.*power.*purchase.*agreement.*',
            r'.*grid.*connection.*',
            r'.*transmission.*line.*',
            r'.*substation.*',
            
            # Financial and conversion data
            r'.*conversion.*cost.*',
            r'.*retrofit.*cost.*',
            r'.*capex.*',
            r'.*opex.*',
        ]
    
    def classify_query(self, query: str) -> str:
        """
        Classify query as 'simple', 'complex', or 'auto'
        
        Args:
            query: Search query string
            
        Returns:
            Classification: 'simple', 'complex', or 'auto'
        """
        query_lower = query.lower()
        
        # Check for complex patterns first (more specific)
        for pattern in self.complex_query_patterns:
            if re.search(pattern, query_lower):
                return 'complex'
        
        # Check for simple patterns
        for pattern in self.simple_query_patterns:
            if re.search(pattern, query_lower):
                return 'simple'
        
        # Default to auto-detection based on query characteristics
        return 'auto'
    
    def should_use_grounded_generation(self, query: str) -> bool:
        """
        Determine if query should use grounded generation
        
        Args:
            query: Search query string
            
        Returns:
            True if should use grounded generation, False for existing system
        """
        classification = self.classify_query(query)
        
        if classification == 'simple':
            return True
        elif classification == 'complex':
            return False
        else:  # auto
            # Auto-detection logic
            query_lower = query.lower()
            
            # Use grounded generation for:
            # 1. Short, direct questions
            # 2. Who/what/when/where questions
            # 3. Single fact queries
            if (len(query.split()) <= 8 and 
                any(word in query_lower for word in ['who', 'what', 'when', 'where', 'is', 'does'])):
                return True
            
            # Use existing system for:
            # 1. Long, complex queries
            # 2. Technical specification searches
            # 3. Multi-parameter queries
            if (len(query.split()) > 15 or
                any(term in query_lower for term in ['specification', 'technical', 'unit', 'efficiency', 'gem.wiki'])):
                return False
            
            # Default to grounded generation for medium queries
            return True
    
    def perform_grounded_search(self, query: str) -> List[Dict[str, Any]]:
        """
        Perform search using Google Gemini grounded generation

        Args:
            query: Search query string

        Returns:
            List of search results
        """
        try:
            print(f"🎯 Using GROUNDED GENERATION for: {query[:60]}...")

            # Add rate limiting to avoid quota issues
            time.sleep(0.5)

            response = self.genai_client.models.generate_content(
                model="gemini-2.5-flash",
                contents=query,
                config=self.grounded_config,
            )

            if response and response.text:
                # Extract grounding information if available
                grounding_info = {}
                if (hasattr(response, 'candidates') and response.candidates
                    and hasattr(response.candidates[0], 'grounding_metadata')):
                    grounding_metadata = response.candidates[0].grounding_metadata
                    if grounding_metadata and hasattr(grounding_metadata, 'grounding_chunks'):
                        grounding_info = {
                            "grounding_chunks": len(grounding_metadata.grounding_chunks),
                            "sources_found": True
                        }

                return [{
                    "title": f"Grounded search: {query}",
                    "content": response.text,
                    "url": "grounded_generation",
                    "search_type": "grounded",
                    "grounded": True,
                    "grounding_metadata": grounding_info
                }]
            else:
                return []

        except Exception as e:
            print(f"⚠️ Grounded search failed for '{query}': {e}")
            return []
    
    def perform_technical_search(self, query: str) -> List[Dict[str, Any]]:
        """
        Perform search using existing sophisticated technical search system
        
        Args:
            query: Search query string
            
        Returns:
            List of search results
        """
        try:
            print(f"🔧 Using TECHNICAL SEARCH for: {query[:60]}...")
            
            # Use existing sophisticated search approach
            configurable = Configuration.from_runnable_config(RunnableConfig(configurable={}))
            current_date = get_current_date()
            
            # Format the search prompt using existing template
            formatted_prompt = web_searcher_instructions.format(
                current_date=current_date,
                research_topic=query
            )
            
            # Use Google Search API through genai client (existing approach)
            response = self.genai_client.models.generate_content(
                model=configurable.web_searcher_model,
                contents=formatted_prompt,
                config={
                    "tools": [{"google_search": {}}],
                    "temperature": 0,
                    "max_output_tokens": 2048,
                    "top_k": 40,
                    "top_p": 0.95,
                }
            )
            
            if response and hasattr(response, 'text'):
                return [{
                    "title": f"Technical search: {query}",
                    "content": response.text,
                    "url": "technical_search",
                    "search_type": "technical",
                    "grounded": False
                }]
            else:
                return []
                
        except Exception as e:
            print(f"⚠️ Technical search failed for '{query}': {e}")
            return []
    
    def search(self, query: str, force_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Main search function that intelligently routes queries

        Args:
            query: Search query string
            force_type: Optional override ('grounded' or 'technical')

        Returns:
            List of search results
        """
        # Log the routing decision
        classification = self.classify_query(query)
        use_grounded = self.should_use_grounded_generation(query)
        print(f"📊 Query classification: {classification}, Using grounded: {use_grounded}")

        if force_type == 'grounded':
            return self.perform_grounded_search(query)
        elif force_type == 'technical':
            return self.perform_technical_search(query)
        else:
            # Intelligent routing
            if use_grounded:
                results = self.perform_grounded_search(query)
                # Fallback to technical search if grounded fails
                if not results:
                    print("🔄 Grounded search failed, falling back to technical search")
                    results = self.perform_technical_search(query)
                return results
            else:
                return self.perform_technical_search(query)

    def get_search_stats(self) -> Dict[str, Any]:
        """
        Get statistics about search usage (for monitoring and optimization)

        Returns:
            Dictionary with search statistics
        """
        # This could be extended to track actual usage statistics
        return {
            "simple_patterns": len(self.simple_query_patterns),
            "complex_patterns": len(self.complex_query_patterns),
            "grounded_generation_available": True,
            "technical_search_available": True
        }


# Global instance for backward compatibility
_hybrid_search_instance = None

def get_hybrid_web_search() -> HybridWebSearch:
    """Get the global hybrid web search instance"""
    global _hybrid_search_instance
    if _hybrid_search_instance is None:
        _hybrid_search_instance = HybridWebSearch()
    return _hybrid_search_instance


def get_enhanced_web_search_function():
    """
    Enhanced web search function that replaces the existing get_web_search_function
    with intelligent hybrid routing between grounded generation and technical search.
    
    Returns:
        Function that performs hybrid web search
    """
    hybrid_search = get_hybrid_web_search()
    
    def perform_hybrid_search(query: str) -> List[Dict[str, Any]]:
        """
        Perform hybrid web search with intelligent routing
        
        Args:
            query: Search query string
            
        Returns:
            List of search results
        """
        return hybrid_search.search(query)
    
    return perform_hybrid_search
