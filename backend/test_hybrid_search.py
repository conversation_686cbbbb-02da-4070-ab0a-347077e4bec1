#!/usr/bin/env python3
"""
Test script for the hybrid web search system

This script tests the new hybrid search implementation with various query types
to ensure proper routing between grounded generation and technical search.
"""

import os
import sys
from dotenv import load_dotenv

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Load environment variables
load_dotenv()

def test_hybrid_search():
    """Test the hybrid search system with various query types"""
    
    print("🧪 Testing Hybrid Web Search System")
    print("=" * 50)
    
    try:
        from agent.hybrid_web_search import HybridWebSearch
        hybrid_search = HybridWebSearch()
        print("✅ Hybrid search system initialized successfully")
    except Exception as e:
        print(f"❌ Failed to initialize hybrid search: {e}")
        return False
    
    # Test queries with expected routing
    test_queries = [
        # Simple factual queries (should use grounded generation)
        {
            "query": "Who owns Antelope Valley Station power plant?",
            "expected_type": "grounded",
            "description": "Simple ownership query"
        },
        {
            "query": "What is the capacity of Antelope Valley Station?",
            "expected_type": "grounded", 
            "description": "Simple capacity query"
        },
        {
            "query": "When was Antelope Valley Station built?",
            "expected_type": "grounded",
            "description": "Simple date query"
        },
        
        # Complex technical queries (should use technical search)
        {
            "query": "Antelope Valley Station Unit 1 technical specifications efficiency heat rate site:gem.wiki",
            "expected_type": "technical",
            "description": "Complex technical query with GEM Wiki"
        },
        {
            "query": "Antelope Valley Station power purchase agreement details transmission grid connection",
            "expected_type": "technical", 
            "description": "Complex PPA and grid query"
        },
        {
            "query": "Unit 2 capacity technology type performance data regulatory filing",
            "expected_type": "technical",
            "description": "Complex unit-specific query"
        },
        
        # Auto-detection queries
        {
            "query": "Antelope Valley Station location",
            "expected_type": "grounded",
            "description": "Short location query"
        },
        {
            "query": "Antelope Valley Station detailed technical specifications and performance metrics",
            "expected_type": "technical",
            "description": "Long technical query"
        }
    ]
    
    print(f"\n🔍 Testing {len(test_queries)} queries...")
    print("-" * 50)
    
    results = []
    for i, test_case in enumerate(test_queries, 1):
        query = test_case["query"]
        expected = test_case["expected_type"]
        description = test_case["description"]
        
        print(f"\n{i}. {description}")
        print(f"   Query: {query}")
        print(f"   Expected: {expected}")
        
        # Test classification
        classification = hybrid_search.classify_query(query)
        should_use_grounded = hybrid_search.should_use_grounded_generation(query)
        actual_type = "grounded" if should_use_grounded else "technical"
        
        print(f"   Classification: {classification}")
        print(f"   Actual routing: {actual_type}")
        
        # Check if routing matches expectation
        routing_correct = actual_type == expected
        status = "✅" if routing_correct else "❌"
        print(f"   Result: {status} {'CORRECT' if routing_correct else 'INCORRECT'}")
        
        results.append({
            "query": query,
            "expected": expected,
            "actual": actual_type,
            "correct": routing_correct,
            "classification": classification
        })
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    
    correct_count = sum(1 for r in results if r["correct"])
    total_count = len(results)
    accuracy = (correct_count / total_count) * 100
    
    print(f"Total queries tested: {total_count}")
    print(f"Correctly routed: {correct_count}")
    print(f"Accuracy: {accuracy:.1f}%")
    
    if accuracy >= 80:
        print("✅ Hybrid search routing is working well!")
    else:
        print("⚠️ Hybrid search routing needs improvement")
    
    # Show incorrect routing details
    incorrect = [r for r in results if not r["correct"]]
    if incorrect:
        print(f"\n❌ Incorrectly routed queries ({len(incorrect)}):")
        for r in incorrect:
            print(f"   - Expected {r['expected']}, got {r['actual']}: {r['query'][:60]}...")
    
    return accuracy >= 80

def test_actual_search():
    """Test actual search functionality with a simple query"""
    
    print("\n🔍 Testing Actual Search Functionality")
    print("=" * 50)
    
    try:
        from agent.registry_nodes import get_web_search_function
        search_fn = get_web_search_function()
        print("✅ Web search function obtained successfully")
        
        # Test with a simple query
        test_query = "Who owns Antelope Valley Station power plant?"
        print(f"\n🌐 Testing query: {test_query}")
        
        results = search_fn(test_query)
        
        if results:
            print(f"✅ Search returned {len(results)} results")
            for i, result in enumerate(results, 1):
                print(f"\n   Result {i}:")
                print(f"   Title: {result.get('title', 'N/A')}")
                print(f"   Type: {result.get('search_type', 'unknown')}")
                print(f"   Grounded: {result.get('grounded', False)}")
                print(f"   Content preview: {result.get('content', '')[:100]}...")
        else:
            print("❌ Search returned no results")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Search test failed: {e}")
        return False

def main():
    """Main test function"""
    
    # Check if API key is available
    if not os.getenv("GEMINI_API_KEY"):
        print("❌ GEMINI_API_KEY not found in environment variables")
        print("Please set your Gemini API key before running tests")
        return False
    
    print("✅ GEMINI_API_KEY found")
    
    # Run tests
    routing_success = test_hybrid_search()
    search_success = test_actual_search()
    
    print("\n" + "=" * 50)
    print("🏁 FINAL RESULTS")
    print("=" * 50)
    
    if routing_success and search_success:
        print("✅ All tests passed! Hybrid search system is ready for use.")
        return True
    else:
        print("❌ Some tests failed. Please review the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
