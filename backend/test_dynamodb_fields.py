#!/usr/bin/env python3
"""
Test script to verify DynamoDB fields removal:
- Removed: created_at, plants_count, session_id, updated_at
- Kept: org_uid, organization_name, country, plant_names
"""

def test_dynamodb_item_structure():
    """Test that DynamoDB item only contains essential fields"""
    print("🔧 Testing DynamoDB Item Structure...")
    
    # Simulate the DynamoDB item creation (as it would be in the actual code)
    def create_organization_item(org_name, org_uid, country, plant_names):
        """Create organization item with only essential fields"""
        item = {
            'org_uid': org_uid,
            'organization_name': org_name,
            'country': country,
            'plant_names': plant_names
        }
        return item
    
    # Test data
    test_data = {
        "org_name": "Test Power Company",
        "org_uid": "test-org-uid-123",
        "country": "United States",
        "plant_names": ["Plant A", "Plant B", "Plant C"]
    }
    
    # Create item
    item = create_organization_item(
        test_data["org_name"],
        test_data["org_uid"],
        test_data["country"],
        test_data["plant_names"]
    )
    
    print(f"   Created DynamoDB item: {item}")
    
    # Check that only essential fields are present
    essential_fields = ['org_uid', 'organization_name', 'country', 'plant_names']
    removed_fields = ['created_at', 'plants_count', 'session_id', 'updated_at']
    
    print("\n   Essential fields check:")
    all_essential_present = True
    for field in essential_fields:
        if field in item:
            print(f"     ✅ {field}: {item[field]}")
        else:
            print(f"     ❌ Missing essential field: {field}")
            all_essential_present = False
    
    print("\n   Removed fields check:")
    no_removed_fields = True
    for field in removed_fields:
        if field in item:
            print(f"     ❌ Removed field still present: {field}")
            no_removed_fields = False
        else:
            print(f"     ✅ {field}: Correctly removed")
    
    # Data type validations
    print("\n   Data type validations:")
    validations = [
        ("org_uid is string", isinstance(item['org_uid'], str)),
        ("organization_name is string", isinstance(item['organization_name'], str)),
        ("country is string", isinstance(item['country'], str)),
        ("plant_names is list", isinstance(item['plant_names'], list)),
        ("plant_names contains strings", all(isinstance(name, str) for name in item['plant_names']))
    ]
    
    all_types_correct = True
    for validation_name, validation_result in validations:
        status = "✅" if validation_result else "❌"
        print(f"     {status} {validation_name}")
        if not validation_result:
            all_types_correct = False
    
    # Overall result
    success = all_essential_present and no_removed_fields and all_types_correct
    
    print(f"\n   Overall result: {'✅ PASS' if success else '❌ FAIL'}")
    return success

def test_dynamodb_manager_structure():
    """Test that DynamoDB manager code has the correct structure"""
    print("\n🔧 Testing DynamoDB Manager Code Structure...")
    
    try:
        # Read the DynamoDB manager file
        with open('src/agent/dynamodb_manager.py', 'r') as f:
            content = f.read()
        
        # Check that removed fields are not in the save method
        removed_fields = ['created_at', 'plants_count', 'session_id', 'updated_at']
        essential_fields = ['org_uid', 'organization_name', 'country', 'plant_names']
        
        print("   Checking save_organization_data method...")
        
        # Find the save method
        save_method_start = content.find('def save_organization_data(')
        if save_method_start == -1:
            print("     ❌ save_organization_data method not found")
            return False
        
        # Extract the method (rough approximation)
        save_method_end = content.find('\n    def ', save_method_start + 1)
        if save_method_end == -1:
            save_method_end = len(content)
        
        save_method_content = content[save_method_start:save_method_end]
        
        # Check for removed fields
        removed_fields_found = []
        for field in removed_fields:
            if f"'{field}'" in save_method_content or f'"{field}"' in save_method_content:
                removed_fields_found.append(field)
        
        if removed_fields_found:
            print(f"     ❌ Removed fields still found in code: {removed_fields_found}")
            return False
        else:
            print("     ✅ No removed fields found in save method")
        
        # Check for essential fields
        essential_fields_found = []
        for field in essential_fields:
            if f"'{field}'" in save_method_content or f'"{field}"' in save_method_content:
                essential_fields_found.append(field)
        
        if len(essential_fields_found) == len(essential_fields):
            print(f"     ✅ All essential fields found: {essential_fields_found}")
        else:
            missing = set(essential_fields) - set(essential_fields_found)
            print(f"     ❌ Missing essential fields: {missing}")
            return False
        
        print("     ✅ DynamoDB manager structure is correct")
        return True
        
    except FileNotFoundError:
        print("     ❌ DynamoDB manager file not found")
        return False
    except Exception as e:
        print(f"     ❌ Error reading DynamoDB manager: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Testing DynamoDB Fields Removal")
    print("=" * 50)
    print("Removed fields: created_at, plants_count, session_id, updated_at")
    print("Kept fields: org_uid, organization_name, country, plant_names")
    print("=" * 50)
    
    tests = [
        ("DynamoDB Item Structure", test_dynamodb_item_structure),
        ("DynamoDB Manager Code", test_dynamodb_manager_structure)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"   ❌ ERROR: {str(e)}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 DynamoDB fields successfully removed!")
        print("✅ Only essential fields remain: org_uid, organization_name, country, plant_names")
        print("✅ Removed fields: created_at, plants_count, session_id, updated_at")
        return 0
    else:
        print("⚠️  Some tests failed. Please review the implementation.")
        return 1

if __name__ == "__main__":
    exit(main())
