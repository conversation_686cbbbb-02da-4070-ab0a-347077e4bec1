#!/usr/bin/env python3
"""
Test script to validate that quick org discovery only returns coal, gas, and oil plants
"""

import os
import sys
from dotenv import load_dotenv

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Load environment variables
load_dotenv()

def test_fuel_filtering():
    """Test that quick org discovery only returns coal, gas, and oil plants"""
    
    print("🔍 Testing Fuel Type Filtering in Quick Org Discovery")
    print("=" * 60)
    
    if not os.getenv("GEMINI_API_KEY"):
        print("❌ GEMINI_API_KEY not found in environment variables")
        return False
    
    try:
        from agent.quick_org_discovery import perform_quick_org_discovery
        from agent.registry_nodes import get_web_search_function
        
        web_search_fn = get_web_search_function()
        
        # Test with a company known to have diverse plant types
        plant_name = "Antelope Valley Station"  # Basin Electric has diverse portfolio
        
        print(f"🌐 Testing with: {plant_name}")
        print("   Expected: Only coal, gas, and oil plants should be returned")
        print("   Should exclude: Nuclear, hydro, solar, wind, renewable plants")
        print("-" * 60)
        
        # Perform discovery
        result = perform_quick_org_discovery(plant_name, web_search_fn)
        
        print(f"\n📊 RESULTS:")
        print(f"   Organization: {result.get('org_name', 'Unknown')}")
        print(f"   Country: {result.get('country', 'Unknown')}")
        print(f"   Total plants found: {len(result.get('plants', []))}")
        
        # Analyze plant types
        plants = result.get('plants', [])
        if not plants:
            print("   ⚠️ No plants found")
            return False
        
        print(f"\n🏭 PLANT ANALYSIS:")
        print("-" * 40)
        
        valid_fuel_types = {'coal', 'gas', 'oil', 'natural gas', 'lng', 'diesel'}
        invalid_fuel_types = {'nuclear', 'hydro', 'solar', 'wind', 'renewable', 'geothermal', 'biomass'}
        
        valid_plants = []
        invalid_plants = []
        unknown_plants = []
        
        for i, plant in enumerate(plants, 1):
            plant_name = plant.get('name', 'Unknown')
            plant_status = plant.get('status', 'unknown')
            fuel_type = plant.get('fuel_type', '').lower()
            
            print(f"   {i:2d}. {plant_name}")
            print(f"       Status: {plant_status}")
            print(f"       Fuel Type: {fuel_type if fuel_type else 'Not specified'}")
            
            # Classify plant
            if fuel_type:
                if any(valid_fuel in fuel_type for valid_fuel in valid_fuel_types):
                    valid_plants.append(plant)
                    print(f"       ✅ VALID (coal/gas/oil)")
                elif any(invalid_fuel in fuel_type for invalid_fuel in invalid_fuel_types):
                    invalid_plants.append(plant)
                    print(f"       ❌ INVALID (should be excluded)")
                else:
                    unknown_plants.append(plant)
                    print(f"       ❓ UNKNOWN fuel type")
            else:
                # Check plant name for fuel type indicators
                plant_name_lower = plant_name.lower()
                if any(valid_fuel in plant_name_lower for valid_fuel in valid_fuel_types):
                    valid_plants.append(plant)
                    print(f"       ✅ VALID (inferred from name)")
                elif any(invalid_fuel in plant_name_lower for invalid_fuel in invalid_fuel_types):
                    invalid_plants.append(plant)
                    print(f"       ❌ INVALID (inferred from name)")
                else:
                    unknown_plants.append(plant)
                    print(f"       ❓ UNKNOWN (no fuel type info)")
            
            print()
        
        # Summary
        print("=" * 60)
        print("📈 FILTERING ANALYSIS:")
        print(f"   ✅ Valid plants (coal/gas/oil): {len(valid_plants)}")
        print(f"   ❌ Invalid plants (should be excluded): {len(invalid_plants)}")
        print(f"   ❓ Unknown fuel type: {len(unknown_plants)}")
        
        # Check filtering effectiveness
        total_plants = len(plants)
        filtering_success = len(invalid_plants) == 0
        
        if filtering_success:
            print(f"\n🎉 FILTERING SUCCESS!")
            print(f"   No invalid plant types found")
            print(f"   All {total_plants} plants appear to be coal, gas, or oil")
        else:
            print(f"\n⚠️ FILTERING NEEDS IMPROVEMENT:")
            print(f"   Found {len(invalid_plants)} plants that should be excluded:")
            for plant in invalid_plants:
                print(f"     - {plant.get('name', 'Unknown')} ({plant.get('fuel_type', 'unknown fuel')})")
        
        if unknown_plants:
            print(f"\n❓ PLANTS WITH UNKNOWN FUEL TYPE ({len(unknown_plants)}):")
            for plant in unknown_plants:
                print(f"     - {plant.get('name', 'Unknown')}")
            print("   These may need manual verification")
        
        return filtering_success
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False

def test_specific_queries():
    """Test specific search queries to see if they're properly filtered"""
    
    print("\n🔍 Testing Specific Search Query Filtering")
    print("=" * 60)
    
    try:
        from agent.hybrid_web_search import get_hybrid_web_search
        
        hybrid_search = get_hybrid_web_search()
        
        # Test queries that should return only coal, gas, oil plants
        test_queries = [
            "Basin Electric Power Cooperative coal gas oil power plants portfolio",
            "List all coal gas oil power plants owned by Basin Electric - exclude nuclear renewable",
            "Basin Electric coal gas oil electricity generation facilities - no hydro solar wind"
        ]
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n{i}. Testing query: {query}")
            print("-" * 40)
            
            results = hybrid_search.search(query)
            
            if results:
                result = results[0]
                content = result.get('content', '')
                search_type = result.get('search_type', 'unknown')
                
                print(f"   Search type: {search_type}")
                print(f"   Content length: {len(content)} characters")
                print(f"   Content preview: {content[:200]}...")
                
                # Check for unwanted plant types in content
                unwanted_terms = ['nuclear', 'hydro', 'solar', 'wind', 'renewable']
                found_unwanted = []
                
                for term in unwanted_terms:
                    if term in content.lower():
                        found_unwanted.append(term)
                
                if found_unwanted:
                    print(f"   ⚠️ Found unwanted terms: {', '.join(found_unwanted)}")
                else:
                    print(f"   ✅ No unwanted plant types mentioned")
            else:
                print(f"   ❌ No results returned")
        
        return True
        
    except Exception as e:
        print(f"❌ Query test failed: {e}")
        return False

def main():
    """Main test function"""
    
    print("🧪 FUEL TYPE FILTERING VALIDATION")
    print("=" * 60)
    
    # Run tests
    discovery_success = test_fuel_filtering()
    query_success = test_specific_queries()
    
    print("\n" + "=" * 60)
    print("🏁 FINAL RESULTS")
    print("=" * 60)
    
    if discovery_success and query_success:
        print("✅ Fuel filtering is working correctly!")
        print("🎯 Only coal, gas, and oil plants are being returned")
        return True
    else:
        print("❌ Fuel filtering needs improvement")
        print("🔧 Review the prompts and filtering logic")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
