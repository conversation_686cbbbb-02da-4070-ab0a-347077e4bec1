#!/usr/bin/env python3
"""
Test script to verify unit field fixes:
- emission_factor_of_gas_unit: "kgCO2e/kg of fuel"
- gcv_biomass_unit: "kCal/kg"
- gcv_coal_unit: "kCal/kg"
"""

import json

def test_unit_level_json():
    """Test that unit_level.json has the correct unit values"""
    print("🔧 Testing unit_level.json...")
    
    try:
        with open('../unit_level.json', 'r') as f:
            data = json.load(f)
        
        # Check emission_factor_of_gas_unit
        gas_unit = data.get("emission_factor_of_gas_unit")
        if gas_unit == "kgCO2e/kg of fuel":
            print("     ✅ emission_factor_of_gas_unit: kgCO2e/kg of fuel (correct)")
        else:
            print(f"     ❌ emission_factor_of_gas_unit: {gas_unit} (expected: kgCO2e/kg of fuel)")
            return False
        
        return True
        
    except FileNotFoundError:
        print("     ❌ unit_level.json not found")
        return False
    except Exception as e:
        print(f"     ❌ Error reading unit_level.json: {e}")
        return False

def test_reference_json():
    """Test that reference.json has the correct unit values"""
    print("\n🔧 Testing reference.json...")
    
    try:
        with open('../reference.json', 'r') as f:
            data = json.load(f)
        
        # Check emission_factor_of_gas_unit
        gas_unit = data.get("emission_factor_of_gas_unit")
        if gas_unit == "kgCO2e/kg of fuel":
            print("     ✅ emission_factor_of_gas_unit: kgCO2e/kg of fuel (correct)")
        else:
            print(f"     ❌ emission_factor_of_gas_unit: {gas_unit} (expected: kgCO2e/kg of fuel)")
            return False
        
        # Check gcv_biomass_unit
        biomass_unit = data.get("gcv_biomass_unit")
        if biomass_unit == "kCal/kg":
            print("     ✅ gcv_biomass_unit: kCal/kg (correct)")
        else:
            print(f"     ❌ gcv_biomass_unit: {biomass_unit} (expected: kCal/kg)")
            return False
        
        # Check gcv_coal_unit
        coal_unit = data.get("gcv_coal_unit")
        if coal_unit == "kCal/kg":
            print("     ✅ gcv_coal_unit: kCal/kg (correct)")
        else:
            print(f"     ❌ gcv_coal_unit: {coal_unit} (expected: kCal/kg)")
            return False
        
        return True
        
    except FileNotFoundError:
        print("     ❌ reference.json not found")
        return False
    except Exception as e:
        print(f"     ❌ Error reading reference.json: {e}")
        return False

def test_unit_extraction_stages():
    """Test that unit_extraction_stages.py has the correct fallback values"""
    print("\n🔧 Testing unit_extraction_stages.py...")
    
    try:
        with open('src/agent/unit_extraction_stages.py', 'r') as f:
            content = f.read()
        
        # Check for the updated values
        checks = [
            ('"emission_factor_of_gas_unit": "kgCO2e/kg of fuel"', 'Gas emission factor unit fixed'),
            ('"gcv_coal_unit": "kCal/kg"', 'Coal GCV unit fixed'),
            ('"gcv_biomass_unit": "kCal/kg"', 'Biomass GCV unit fixed'),
        ]
        
        all_checks_passed = True
        
        for check_text, description in checks:
            if check_text in content:
                print(f"     ✅ {description}")
            else:
                print(f"     ❌ {description}: Not found")
                all_checks_passed = False
        
        return all_checks_passed
        
    except FileNotFoundError:
        print("     ❌ unit_extraction_stages.py not found")
        return False
    except Exception as e:
        print(f"     ❌ Error reading unit_extraction_stages.py: {e}")
        return False

def test_fallback_calculations():
    """Test that fallback_calculations.py has the correct unit assignments"""
    print("\n🔧 Testing fallback_calculations.py...")
    
    try:
        with open('src/agent/fallback_calculations.py', 'r') as f:
            content = f.read()
        
        # Check for the updated values
        checks = [
            ('"gcv_coal_unit"] = "kCal/kg"', 'Coal GCV unit in fallback'),
            ('"gcv_biomass_unit"] = "kCal/kg"', 'Biomass GCV unit in fallback'),
        ]
        
        all_checks_passed = True
        
        for check_text, description in checks:
            if check_text in content:
                print(f"     ✅ {description}")
            else:
                print(f"     ❌ {description}: Not found")
                all_checks_passed = False
        
        return all_checks_passed
        
    except FileNotFoundError:
        print("     ❌ fallback_calculations.py not found")
        return False
    except Exception as e:
        print(f"     ❌ Error reading fallback_calculations.py: {e}")
        return False

def test_graph_templates():
    """Test that graph.py templates have the correct unit values"""
    print("\n🔧 Testing graph.py templates...")
    
    try:
        with open('src/agent/graph.py', 'r') as f:
            content = f.read()
        
        # Check for the updated values in templates
        checks = [
            ('"gcv_biomass_unit": "kCal/kg"', 'Biomass GCV unit in templates'),
            ('"emission_factor_of_gas_unit": "kgCO2e/kg of fuel"', 'Gas emission factor unit in templates'),
        ]
        
        all_checks_passed = True
        
        for check_text, description in checks:
            if check_text in content:
                print(f"     ✅ {description}")
            else:
                print(f"     ❌ {description}: Not found")
                all_checks_passed = False
        
        return all_checks_passed
        
    except FileNotFoundError:
        print("     ❌ graph.py not found")
        return False
    except Exception as e:
        print(f"     ❌ Error reading graph.py: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Testing Unit Field Fixes")
    print("=" * 60)
    print("Required values:")
    print("  emission_factor_of_gas_unit: kgCO2e/kg of fuel")
    print("  gcv_biomass_unit: kCal/kg")
    print("  gcv_coal_unit: kCal/kg")
    print("=" * 60)
    
    tests = [
        ("unit_level.json", test_unit_level_json),
        ("reference.json", test_reference_json),
        ("unit_extraction_stages.py", test_unit_extraction_stages),
        ("fallback_calculations.py", test_fallback_calculations),
        ("graph.py templates", test_graph_templates)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"   ❌ ERROR in {test_name}: {str(e)}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 Unit field fixes completed successfully!")
        print("✅ emission_factor_of_gas_unit: kgCO2e/kg of fuel")
        print("✅ gcv_biomass_unit: kCal/kg")
        print("✅ gcv_coal_unit: kCal/kg")
        print("✅ All files updated with correct unit values")
        return 0
    else:
        print("⚠️  Some tests failed. Please review the implementation.")
        return 1

if __name__ == "__main__":
    exit(main())
