#!/usr/bin/env python3
"""
Test script to verify gcv_natural_gas and gcv_natural_gas_unit field fixes
"""

import json

def test_unit_level_json():
    """Test that unit_level.json has the correct values"""
    print("🔧 Testing unit_level.json...")
    
    try:
        with open('../unit_level.json', 'r') as f:
            data = json.load(f)
        
        # Check gcv_natural_gas value
        gcv_value = data.get("gcv_natural_gas")
        if gcv_value == "10000":
            print("     ✅ gcv_natural_gas: 10000 (correct)")
        else:
            print(f"     ❌ gcv_natural_gas: {gcv_value} (expected: 10000)")
            return False
        
        # Check gcv_natural_gas_unit value
        gcv_unit = data.get("gcv_natural_gas_unit")
        if gcv_unit == "kcal/scm":
            print("     ✅ gcv_natural_gas_unit: kcal/scm (correct)")
        else:
            print(f"     ❌ gcv_natural_gas_unit: {gcv_unit} (expected: kcal/scm)")
            return False
        
        return True
        
    except FileNotFoundError:
        print("     ❌ unit_level.json not found")
        return False
    except Exception as e:
        print(f"     ❌ Error reading unit_level.json: {e}")
        return False

def test_reference_json():
    """Test that reference.json has the correct values"""
    print("\n🔧 Testing reference.json...")
    
    try:
        with open('../reference.json', 'r') as f:
            data = json.load(f)
        
        # Check gcv_natural_gas value
        gcv_value = data.get("gcv_natural_gas")
        if gcv_value == 10000:
            print("     ✅ gcv_natural_gas: 10000 (correct)")
        else:
            print(f"     ❌ gcv_natural_gas: {gcv_value} (expected: 10000)")
            return False
        
        # Check gcv_natural_gas_unit value
        gcv_unit = data.get("gcv_natural_gas_unit")
        if gcv_unit == "kcal/scm":
            print("     ✅ gcv_natural_gas_unit: kcal/scm (correct)")
        else:
            print(f"     ❌ gcv_natural_gas_unit: {gcv_unit} (expected: kcal/scm)")
            return False
        
        return True
        
    except FileNotFoundError:
        print("     ❌ reference.json not found")
        return False
    except Exception as e:
        print(f"     ❌ Error reading reference.json: {e}")
        return False

def test_reference_data_py():
    """Test that reference_data.py has the correct values"""
    print("\n🔧 Testing reference_data.py...")
    
    try:
        with open('src/agent/reference_data.py', 'r') as f:
            content = f.read()
        
        # Check for the updated values
        checks = [
            ('"gcv": 10000', 'GCV value updated to 10000'),
            ('"gcv_unit": "kcal/scm"', 'GCV unit updated to kcal/scm'),
        ]
        
        all_checks_passed = True
        
        for check_text, description in checks:
            if check_text in content:
                print(f"     ✅ {description}")
            else:
                print(f"     ❌ {description}: Not found")
                all_checks_passed = False
        
        return all_checks_passed
        
    except FileNotFoundError:
        print("     ❌ reference_data.py not found")
        return False
    except Exception as e:
        print(f"     ❌ Error reading reference_data.py: {e}")
        return False

def test_tools_and_schemas_py():
    """Test that tools_and_schemas.py has the correct default values"""
    print("\n🔧 Testing tools_and_schemas.py...")
    
    try:
        with open('src/agent/tools_and_schemas.py', 'r') as f:
            content = f.read()
        
        # Check for the updated default values
        checks = [
            ('default=10000', 'Default GCV value set to 10000'),
            ('default="kcal/scm"', 'Default GCV unit set to kcal/scm'),
        ]
        
        all_checks_passed = True
        
        for check_text, description in checks:
            if check_text in content:
                print(f"     ✅ {description}")
            else:
                print(f"     ❌ {description}: Not found")
                all_checks_passed = False
        
        return all_checks_passed
        
    except FileNotFoundError:
        print("     ❌ tools_and_schemas.py not found")
        return False
    except Exception as e:
        print(f"     ❌ Error reading tools_and_schemas.py: {e}")
        return False

def test_unit_extraction_stages_py():
    """Test that unit_extraction_stages.py has the correct example and fallback values"""
    print("\n🔧 Testing unit_extraction_stages.py...")
    
    try:
        with open('src/agent/unit_extraction_stages.py', 'r') as f:
            content = f.read()
        
        # Check for the updated values
        checks = [
            ('"gcv_natural_gas": "10000"', 'Example GCV value updated to 10000'),
            ('"gcv_natural_gas_unit": "kcal/scm"', 'Example and fallback GCV unit updated to kcal/scm'),
        ]
        
        all_checks_passed = True
        
        for check_text, description in checks:
            if check_text in content:
                print(f"     ✅ {description}")
            else:
                print(f"     ❌ {description}: Not found")
                all_checks_passed = False
        
        return all_checks_passed
        
    except FileNotFoundError:
        print("     ❌ unit_extraction_stages.py not found")
        return False
    except Exception as e:
        print(f"     ❌ Error reading unit_extraction_stages.py: {e}")
        return False

def test_fallback_calculations_py():
    """Test that fallback_calculations.py has the correct unit"""
    print("\n🔧 Testing fallback_calculations.py...")
    
    try:
        with open('src/agent/fallback_calculations.py', 'r') as f:
            content = f.read()
        
        # Check for the updated unit
        if '"kcal/scm"' in content:
            print("     ✅ Fallback GCV unit updated to kcal/scm")
            return True
        else:
            print("     ❌ Fallback GCV unit not updated to kcal/scm")
            return False
        
    except FileNotFoundError:
        print("     ❌ fallback_calculations.py not found")
        return False
    except Exception as e:
        print(f"     ❌ Error reading fallback_calculations.py: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Testing GCV Natural Gas Field Fixes")
    print("=" * 60)
    print("Required values:")
    print("  gcv_natural_gas: 10000")
    print("  gcv_natural_gas_unit: kcal/scm")
    print("=" * 60)
    
    tests = [
        ("unit_level.json", test_unit_level_json),
        ("reference.json", test_reference_json),
        ("reference_data.py", test_reference_data_py),
        ("tools_and_schemas.py", test_tools_and_schemas_py),
        ("unit_extraction_stages.py", test_unit_extraction_stages_py),
        ("fallback_calculations.py", test_fallback_calculations_py)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"   ❌ ERROR in {test_name}: {str(e)}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 GCV Natural Gas fields fixed successfully!")
        print("✅ gcv_natural_gas: 10000")
        print("✅ gcv_natural_gas_unit: kcal/scm")
        print("✅ All files updated with correct values")
        return 0
    else:
        print("⚠️  Some tests failed. Please review the implementation.")
        return 1

if __name__ == "__main__":
    exit(main())
