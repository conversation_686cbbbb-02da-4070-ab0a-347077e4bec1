#!/usr/bin/env python3
"""
Test script to verify the two field changes:
1. Field name changes (old -> new)
2. Mandatory closure logic (Yes/No only)
"""

import sys
import os
import re

def test_field_name_changes():
    """Test that old field names have been changed to new field names"""
    print("🔧 Testing Field Name Changes...")
    
    # Define the field mappings
    field_mappings = {
        "blending_percentage": "blending_percentage_of_biomass",
        "capex_required_retrofit": "capex_required_retrofit_biomass", 
        "capex_required_retrofit_unit": "capex_required_retrofit_biomass_unit",
        "efficiency_loss_cofiring": "efficiency_loss_biomass_cofiring",
        "emission_factor_coking_coal": "emission_factor_coal",
        "unit_efficiency": "coal_unit_efficiency"
    }
    
    # Files to check
    files_to_check = [
        "src/agent/unit_extraction_stages.py",
        "src/agent/fallback_calculations.py",
        "src/agent/precision_prompts.py",
        "src/agent/tools_and_schemas.py",
        "../reference.json",
        "../unit_level.json"
    ]
    
    all_passed = True
    
    for file_path in files_to_check:
        print(f"\n   Checking file: {file_path}")
        
        try:
            with open(file_path, 'r') as f:
                content = f.read()
            
            # Check for old field names (should not exist)
            old_fields_found = []
            for old_field, new_field in field_mappings.items():
                # Use regex to find exact field matches (not partial matches)
                # Look for the old field name as a key/field, not as part of another field
                pattern = rf'["\']?{re.escape(old_field)}["\']?\s*[:=]'
                matches = re.findall(pattern, content)

                # Filter out false positives (e.g., coal_unit_efficiency containing unit_efficiency)
                valid_matches = []
                for match in matches:
                    # Check if this is actually the old field and not part of the new field
                    if old_field == "unit_efficiency":
                        # Make sure it's not "coal_unit_efficiency"
                        if "coal_unit_efficiency" not in content[content.find(match)-20:content.find(match)+len(match)+20]:
                            valid_matches.append(match)
                    else:
                        valid_matches.append(match)

                if valid_matches:
                    old_fields_found.append(old_field)
            
            # Check for new field names (should exist)
            new_fields_found = []
            for old_field, new_field in field_mappings.items():
                pattern = rf'["\']?{re.escape(new_field)}["\']?\s*[:=]'
                if re.search(pattern, content):
                    new_fields_found.append(new_field)
            
            if old_fields_found:
                print(f"     ❌ Old field names still found: {old_fields_found}")
                all_passed = False
            else:
                print(f"     ✅ No old field names found")
            
            if new_fields_found:
                print(f"     ✅ New field names found: {new_fields_found}")
            else:
                print(f"     ℹ️ No new field names found (may be expected for some files)")
                
        except FileNotFoundError:
            print(f"     ⚠️ File not found: {file_path}")
        except Exception as e:
            print(f"     ❌ Error reading file: {e}")
            all_passed = False
    
    return all_passed

def test_mandatory_closure_logic():
    """Test that mandatory closure logic returns Yes/No only"""
    print("\n🔧 Testing Mandatory Closure Logic...")
    
    # Simulate the mandatory closure logic
    def process_mandatory_closure(mandatory_text):
        """Simulate the new mandatory closure processing logic"""
        mandatory_text_lower = mandatory_text.strip().lower()
        
        # Check for policy indicators
        has_policy = False
        has_specific_year = False
        
        policy_indicators = [
            'company policy', 'internal policy', 'holding company',
            'government regulation', 'local regulation', 'regional regulation', 
            'decarbonization', 'coal phase-out', 'phase out', 'closure policy',
            'mandatory closure', 'required closure', 'scheduled closure'
        ]
        
        for indicator in policy_indicators:
            if indicator in mandatory_text_lower:
                has_policy = True
                break
        
        # Check for specific year mention
        year_match = re.search(r'\b(20\d{2})\b', mandatory_text_lower)
        if year_match:
            has_specific_year = True
        
        # Set YES only if policy exists AND specific year is mentioned
        if has_policy and has_specific_year:
            return "Yes"
        else:
            return "No"
    
    # Test cases
    test_cases = [
        {
            "input": "The government has announced a coal phase-out policy requiring all coal plants to close by 2030.",
            "expected": "Yes",
            "description": "Government policy with specific year"
        },
        {
            "input": "The holding company has an internal policy to phase out coal operations by 2035.",
            "expected": "Yes", 
            "description": "Company policy with specific year"
        },
        {
            "input": "Local regulation requires decarbonization but no specific closure date has been set.",
            "expected": "No",
            "description": "Policy exists but no specific year"
        },
        {
            "input": "The plant operates under normal regulations with no closure requirements.",
            "expected": "No",
            "description": "No policy or closure requirement"
        },
        {
            "input": "There is discussion about potential closure in the future.",
            "expected": "No",
            "description": "Vague discussion, no specific policy or year"
        },
        {
            "input": "The national decarbonization strategy mentions coal phase-out by 2040 for this region.",
            "expected": "Yes",
            "description": "National policy with specific year"
        },
        {
            "input": "",
            "expected": "No",
            "description": "Empty input"
        }
    ]
    
    all_passed = True
    
    for i, test_case in enumerate(test_cases, 1):
        actual = process_mandatory_closure(test_case["input"])
        expected = test_case["expected"]
        
        print(f"\n   Test Case {i}: {test_case['description']}")
        print(f"     Input: '{test_case['input']}'")
        print(f"     Expected: {expected}")
        print(f"     Actual: {actual}")
        
        if actual == expected:
            print(f"     ✅ PASS")
        else:
            print(f"     ❌ FAIL")
            all_passed = False
    
    return all_passed

def test_code_integration():
    """Test that the changes are properly integrated in the code"""
    print("\n🔧 Testing Code Integration...")
    
    # Check graph.py for mandatory closure logic
    try:
        with open('src/agent/graph.py', 'r') as f:
            graph_content = f.read()
        
        # Check for the new mandatory closure logic
        checks = [
            ('has_policy and has_specific_year', 'New mandatory closure logic'),
            ('plant_data["mandatory_closure"] = "Yes"', 'Yes assignment'),
            ('plant_data["mandatory_closure"] = "No"', 'No assignment'),
            ('policy_indicators = [', 'Policy indicators list'),
        ]
        
        all_checks_passed = True
        
        print("   Checking mandatory closure logic in graph.py:")
        for check_text, description in checks:
            if check_text in graph_content:
                print(f"     ✅ {description}: Found")
            else:
                print(f"     ❌ {description}: Not found")
                all_checks_passed = False
        
        return all_checks_passed
        
    except FileNotFoundError:
        print("     ❌ graph.py not found")
        return False
    except Exception as e:
        print(f"     ❌ Error reading graph.py: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Testing Field Changes Implementation")
    print("=" * 60)
    print("Change 1: Field name updates (6 field mappings)")
    print("Change 2: Mandatory closure logic (Yes/No only)")
    print("=" * 60)
    
    tests = [
        ("Field Name Changes", test_field_name_changes),
        ("Mandatory Closure Logic", test_mandatory_closure_logic),
        ("Code Integration", test_code_integration)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 40)
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"   ❌ ERROR: {str(e)}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All field changes implemented successfully!")
        print("✅ Field names updated: 6 mappings applied")
        print("✅ Mandatory closure logic: Returns Yes/No only")
        print("✅ Code integration: Properly integrated in pipeline")
        return 0
    else:
        print("⚠️  Some tests failed. Please review the implementation.")
        return 1

if __name__ == "__main__":
    exit(main())
