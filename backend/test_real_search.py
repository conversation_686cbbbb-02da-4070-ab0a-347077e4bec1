#!/usr/bin/env python3
"""
Real-world test of the hybrid search system with actual power plant queries
"""

import os
import sys
from dotenv import load_dotenv

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Load environment variables
load_dotenv()

def test_real_searches():
    """Test with real power plant queries"""
    
    print("🌐 Testing Real Power Plant Searches")
    print("=" * 60)
    
    try:
        from agent.registry_nodes import get_web_search_function
        search_fn = get_web_search_function()
        print("✅ Web search function obtained successfully")
    except Exception as e:
        print(f"❌ Failed to get search function: {e}")
        return False
    
    # Real test queries
    test_queries = [
        {
            "query": "Who owns Antelope Valley Station power plant?",
            "description": "Simple ownership query (should use grounded generation)",
            "expected_type": "grounded"
        },
        {
            "query": "Antelope Valley Station Unit 1 capacity MW technology type site:gem.wiki",
            "description": "Complex technical query (should use technical search)",
            "expected_type": "technical"
        },
        {
            "query": "What is the retirement date of Antelope Valley Station?",
            "description": "Simple factual query (should use grounded generation)",
            "expected_type": "grounded"
        }
    ]
    
    for i, test_case in enumerate(test_queries, 1):
        query = test_case["query"]
        description = test_case["description"]
        expected_type = test_case["expected_type"]
        
        print(f"\n{i}. {description}")
        print(f"   Query: {query}")
        print(f"   Expected type: {expected_type}")
        print("   " + "-" * 50)
        
        try:
            results = search_fn(query)
            
            if results:
                result = results[0]  # Get first result
                search_type = result.get('search_type', 'unknown')
                grounded = result.get('grounded', False)
                content = result.get('content', '')
                
                print(f"   ✅ Search successful")
                print(f"   Search type: {search_type}")
                print(f"   Grounded: {grounded}")
                print(f"   Content length: {len(content)} characters")
                print(f"   Content preview: {content[:200]}...")
                
                # Check if routing was correct
                actual_type = "grounded" if grounded else "technical"
                routing_correct = actual_type == expected_type
                status = "✅" if routing_correct else "❌"
                print(f"   Routing: {status} {'CORRECT' if routing_correct else 'INCORRECT'}")
                
            else:
                print(f"   ❌ No results returned")
                
        except Exception as e:
            print(f"   ❌ Search failed: {e}")
    
    return True

def test_pipeline_integration():
    """Test integration with existing pipeline components"""
    
    print("\n🔧 Testing Pipeline Integration")
    print("=" * 60)
    
    try:
        # Test quick org discovery (uses web search function)
        from agent.quick_org_discovery import perform_quick_org_discovery
        from agent.registry_nodes import get_web_search_function
        
        web_search_fn = get_web_search_function()
        plant_name = "Antelope Valley Station"
        
        print(f"🔍 Testing quick org discovery for: {plant_name}")
        
        # This will use our hybrid search system
        result = perform_quick_org_discovery(plant_name, web_search_fn)
        
        print(f"✅ Quick discovery completed")
        print(f"   Organization: {result.get('org_name', 'Unknown')}")
        print(f"   Country: {result.get('country', 'Unknown')}")
        print(f"   Plants found: {len(result.get('plants', []))}")
        
        return True
        
    except Exception as e:
        print(f"❌ Pipeline integration test failed: {e}")
        return False

def main():
    """Main test function"""
    
    if not os.getenv("GEMINI_API_KEY"):
        print("❌ GEMINI_API_KEY not found in environment variables")
        return False
    
    print("✅ GEMINI_API_KEY found")
    
    # Run tests
    search_success = test_real_searches()
    integration_success = test_pipeline_integration()
    
    print("\n" + "=" * 60)
    print("🏁 REAL-WORLD TEST RESULTS")
    print("=" * 60)
    
    if search_success and integration_success:
        print("✅ All real-world tests passed!")
        print("🚀 Hybrid search system is ready for production use!")
        return True
    else:
        print("❌ Some tests failed. Please review the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
