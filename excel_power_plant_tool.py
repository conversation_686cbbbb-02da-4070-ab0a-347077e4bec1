import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
from typing import Dict, List, Any, Optional
import logging
import re
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import sys
import os

# Add the backend/src/agent directory to the path to import the calculator
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend', 'src', 'agent'))
try:
    from heat_rate_efficiency_calculator import HeatRateEfficiencyCalculator
except ImportError:
    # Fallback if the path is different
    sys.path.append(os.path.dirname(__file__))
    from heat_rate_efficiency_calculator import HeatRateEfficiencyCalculator

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def normalize_plant_name(name: str) -> str:
    suffixes = [' Power Plant', ' Generating Plant', ' Power Station', ' Generating Station', ' Energy Center', ' Center', ' Power Complex', ' Generating Complex', ' Power Facility', ' Generating Facility', ' Power Project', ' Generating Project', ' Power Site', ' Generating Site', ' Power Area', ' Generating Area', ' Plant', ' Station', ' Fossil Plant', ' Generating Fossil Plant', ' Generation Fossil Plant']
    for s in suffixes:
        if name.endswith(s):
            name = name[:-len(s)]
            break
    return name.strip()

class ExcelPowerPlantTool:
    """
    Excel-based power plant data tool for USA power plant information.
    Processes multiple sheets from USA Details.xlsx to provide structured data
    according to unit_level.json schema.
    """
    
    def __init__(self, excel_file_path: str = "USA Details.xlsx", session_id: str = ""):
        """
        Initialize the Excel Power Plant Tool.

        Args:
            excel_file_path: Path to the Excel file containing power plant data
            session_id: Session ID for logging
        """
        self.excel_file_path = excel_file_path
        self.session_id = session_id
        self.usa_details = None
        self.coal_data = None
        self.emission_factors_coal = None
        self.vectorizer = None
        self.plant_vectors = None
        self.plant_descriptions = None

        # Initialize heat rate and efficiency calculator
        self.heat_rate_calculator = HeatRateEfficiencyCalculator(session_id)

        # Load and process all data
        self._load_and_process_data()

        # Initialize vector search
        self._initialize_vector_search()
    
    def _load_and_process_data(self):
        """Load and process all Excel sheets."""
        logger.info("Loading Excel data...")
        
        try:
            # Process each sheet
            self.usa_details = self._process_usa_details()
            self.coal_data = self._consolidate_coal_sheets()
            self.emission_factors_coal = self._load_coal_emission_factors()
            
            logger.info("Excel data loaded and processed successfully")
            
        except Exception as e:
            logger.error(f"Error loading Excel data: {e}")
            raise
    
    def _process_usa_details(self) -> pd.DataFrame:
        """
        Process the USA Details sheet.
        
        Returns:
            Processed DataFrame with cleaned and normalized data
        """
        logger.info("Processing USA Details sheet...")
        
        # Read USA Details sheet
        df = pd.read_excel(self.excel_file_path, sheet_name='USA Details')
        
        # Clean column names and remove empty rows
        df = df.dropna(subset=['Entity Name', 'Plant Name', 'Unit IDs'])

        # UPDATED: Process ALL plant types (coal, gas, nuclear, etc.)
        # Remove coal-only filter to support all power plant technologies
        logger.info(f"Processing {len(df)} plants of all types from USA Details sheet")

        # Convert data types
        df['Capacity'] = pd.to_numeric(df['Capacity'], errors='coerce')
        df['Operating Month'] = pd.to_numeric(df['Operating Month'], errors='coerce')
        df['Operating Year'] = pd.to_numeric(df['Operating Year'], errors='coerce')
        df['Planned Retirement Month'] = pd.to_numeric(df['Planned Retirement Month'], errors='coerce')
        df['Planned Retirement Year'] = pd.to_numeric(df['Planned Retirement Year'], errors='coerce')
        
        # Create commencement_date timestamp
        df['commencement_date'] = df.apply(self._create_timestamp, axis=1, 
                                         month_col='Operating Month', year_col='Operating Year')
        
        # Calculate unit_lifetime in years first (needed for remaining_useful_life)
        df['unit_lifetime'] = df.apply(self._calculate_unit_lifetime_from_retirement, axis=1)
        logger.info(f"[Session {self.session_id}] 🔧 Unit lifetime calculated for {len(df)} units")

        # Create remaining_useful_life timestamp (user's formula: commencement_date + unit_lifetime)
        df['remaining_useful_life'] = df.apply(self._calculate_remaining_useful_life, axis=1)
        logger.info(f"[Session {self.session_id}] 🔧 Remaining useful life calculated for {len(df)} units")

        # Debug: Check for null values
        null_lifetime = df['unit_lifetime'].isnull().sum()
        null_remaining = df['remaining_useful_life'].isnull().sum()
        if null_lifetime > 0:
            logger.warning(f"[Session {self.session_id}] ⚠️ Found {null_lifetime} null unit_lifetime values")
        if null_remaining > 0:
            logger.warning(f"[Session {self.session_id}] ⚠️ Found {null_remaining} null remaining_useful_life values")
        
        # Create unique plant-unit identifier
        df['plant_unit_key'] = df['Plant Name'].astype(str) + "_" + df['Unit IDs'].astype(str)
        
        logger.info(f"Processed {len(df)} units from USA Details sheet")
        return df
    
    def _create_timestamp(self, row, month_col: str, year_col: str) -> str:
        """
        Create ISO timestamp from month and year columns.
        
        Args:
            row: DataFrame row
            month_col: Column name for month
            year_col: Column name for year
            
        Returns:
            ISO timestamp string
        """
        try:
            month = int(row[month_col]) if pd.notna(row[month_col]) else 1
            year = int(row[year_col]) if pd.notna(row[year_col]) else 2000
            
            # Ensure month is valid (1-12)
            month = max(1, min(12, month))
            
            # Create datetime and convert to ISO format
            dt = datetime(year, month, 1)
            return dt.strftime('%Y-%m-%dT%H:%M:%S.%f')[:-3] + 'Z'
            
        except (ValueError, TypeError):
            # Default timestamp if conversion fails
            return "2000-01-01T00:00:00.000Z"
    
    def _create_retirement_timestamp(self, row) -> str:
        """
        Create retirement timestamp with 50-year rule for missing data.
        
        Args:
            row: DataFrame row
            
        Returns:
            ISO timestamp string for retirement date
        """
        try:
            # Check if retirement data is available
            if pd.notna(row['Planned Retirement Month']) and pd.notna(row['Planned Retirement Year']):
                month = int(row['Planned Retirement Month'])
                year = int(row['Planned Retirement Year'])
            else:
                # Apply 50-year rule
                operating_year = int(row['Operating Year']) if pd.notna(row['Operating Year']) else 2000
                month = int(row['Operating Month']) if pd.notna(row['Operating Month']) else 1
                year = operating_year + 50
            
            # Ensure month is valid
            month = max(1, min(12, month))
            
            # Create datetime and convert to ISO format
            dt = datetime(year, month, 1)
            return dt.strftime('%Y-%m-%dT%H:%M:%S.%f')[:-3] + 'Z'
            
        except (ValueError, TypeError):
            # Default to 50 years from 2000 if all else fails
            return "2050-01-01T00:00:00.000Z"
    
    def _calculate_unit_lifetime_from_retirement(self, row) -> float:
        """
        Calculate unit lifetime in years from commencement to retirement.

        Args:
            row: DataFrame row

        Returns:
            Unit lifetime in years
        """
        try:
            # Parse commencement date
            start_date = datetime.strptime(row['commencement_date'], '%Y-%m-%dT%H:%M:%S.%fZ')

            # Calculate retirement date using 50-year rule or planned retirement
            if pd.notna(row['Planned Retirement Month']) and pd.notna(row['Planned Retirement Year']):
                month = int(row['Planned Retirement Month'])
                year = int(row['Planned Retirement Year'])
            else:
                # Apply 50-year rule
                operating_year = int(row['Operating Year']) if pd.notna(row['Operating Year']) else 2000
                month = int(row['Operating Month']) if pd.notna(row['Operating Month']) else 1
                year = operating_year + 50

            # Ensure month is valid
            month = max(1, min(12, month))
            end_date = datetime(year, month, 1)

            # Calculate difference in years
            lifetime = (end_date - start_date).days / 365.25
            return round(lifetime, 2)

        except (ValueError, TypeError):
            # Default to 50 years if calculation fails
            return 50.0

    def _calculate_remaining_useful_life(self, row) -> str:
        """
        Calculate remaining useful life using user's formula: commencement_date + unit_lifetime

        Args:
            row: DataFrame row

        Returns:
            ISO timestamp string for remaining useful life date
        """
        try:
            # Parse commencement date
            commencement_date = datetime.strptime(row['commencement_date'], '%Y-%m-%dT%H:%M:%S.%fZ')

            # Get unit lifetime in years
            unit_lifetime_years = float(row['unit_lifetime'])

            # Debug logging
            plant_name = row.get('Plant Name', 'Unknown')
            unit_id = row.get('Unit IDs', 'Unknown')
            logger.debug(f"[Session {self.session_id}] 🔧 Calculating remaining life for {plant_name} Unit {unit_id}: "
                        f"commencement={row['commencement_date']}, lifetime={unit_lifetime_years} years")

            # Add unit_lifetime to commencement_date
            remaining_date = commencement_date + timedelta(days=unit_lifetime_years * 365.25)

            result = remaining_date.strftime('%Y-%m-%dT%H:%M:%S.%f')[:-3] + 'Z'
            logger.debug(f"[Session {self.session_id}] ✅ Remaining useful life: {result}")
            return result

        except (ValueError, TypeError) as e:
            # Debug logging for errors
            plant_name = row.get('Plant Name', 'Unknown')
            unit_id = row.get('Unit IDs', 'Unknown')
            logger.warning(f"[Session {self.session_id}] ⚠️ Error calculating remaining life for {plant_name} Unit {unit_id}: {e}")

            # Default to 50 years from commencement if calculation fails
            try:
                commencement_date = datetime.strptime(row['commencement_date'], '%Y-%m-%dT%H:%M:%S.%fZ')
                default_end = commencement_date + timedelta(days=50 * 365.25)
                result = default_end.strftime('%Y-%m-%dT%H:%M:%S.%f')[:-3] + 'Z'
                logger.debug(f"[Session {self.session_id}] 🔄 Using fallback remaining life: {result}")
                return result
            except:
                logger.warning(f"[Session {self.session_id}] ❌ All remaining life calculations failed, using default")
                return "2050-01-01T00:00:00.000Z"

    def _consolidate_coal_sheets(self) -> pd.DataFrame:
        """
        Consolidate all Coal yearly sheets (2020-2024).
        UPDATED: Now includes all necessary columns for calculations.

        Returns:
            Consolidated DataFrame with all coal data including generation and fuel consumption
        """
        logger.info("Consolidating Coal yearly sheets...")

        coal_sheets = ['Coal 2024', 'Coal 2023', 'Coal 2022', 'Coal 2021', 'Coal 2020']
        all_coal_data = []

        for sheet_name in coal_sheets:
            try:
                # Extract year from sheet name
                year = sheet_name.split(' ')[1]

                # Read coal sheet
                df = pd.read_excel(self.excel_file_path, sheet_name=sheet_name)

                # Clean and standardize column names
                df = df.dropna(subset=['Plant Name'])

                # Add year column
                df['Year'] = year

                # Standardize operator column name
                if 'Entity Name' in df.columns:
                    df['Operator Name'] = df['Entity Name']

                # Select ALL relevant columns for calculations (not just basic info)
                coal_cols = ['Plant Name', 'Operator Name', 'Type', 'Fuel', 'Emission Factor',
                            'Electric Fuel Consumption Quantity', 'Net Generation (Megawatthours)', 'Year']

                # Only include columns that exist
                available_cols = [col for col in coal_cols if col in df.columns]
                df_clean = df[available_cols].copy()

                # Convert numeric columns
                numeric_cols = ['Emission Factor', 'Electric Fuel Consumption Quantity', 'Net Generation (Megawatthours)']
                for col in numeric_cols:
                    if col in df_clean.columns:
                        df_clean[col] = pd.to_numeric(df_clean[col], errors='coerce')

                # Remove rows with zero or null emission factors
                df_clean = df_clean[df_clean['Emission Factor'] > 0] if 'Emission Factor' in df_clean.columns else df_clean

                all_coal_data.append(df_clean)
                logger.info(f"Processed {len(df_clean)} records from {sheet_name}")

            except Exception as e:
                logger.warning(f"Error processing {sheet_name}: {e}")
                continue

        # Combine all coal data
        if all_coal_data:
            consolidated_df = pd.concat(all_coal_data, ignore_index=True)
            logger.info(f"Consolidated {len(consolidated_df)} total coal records")
            return consolidated_df
        else:
            logger.warning("No coal data could be processed")
            return pd.DataFrame()

    def _load_coal_emission_factors(self) -> Dict[str, float]:
        """
        Load emission factor coal mapping from Full Names sheet.

        Returns:
            Dictionary mapping coal types to emission factors
        """
        logger.info("Loading coal emission factors from Full Names sheet...")

        try:
            # Read Full Names sheet
            df = pd.read_excel(self.excel_file_path, sheet_name='Full Names')

            # Clean column names
            df.columns = ['coal_type', 'emission_factor_value']

            # Remove null values
            df = df.dropna()

            # Convert to dictionary
            emission_factors = dict(zip(df['coal_type'], df['emission_factor_value']))

            logger.info(f"Loaded {len(emission_factors)} coal emission factor mappings")
            return emission_factors

        except Exception as e:
            logger.error(f"Error loading coal emission factors: {e}")
            return {}

    def _initialize_vector_search(self):
        """Initialize vector search capabilities for semantic querying."""
        logger.info("Initializing vector search...")

        try:
            # Create text descriptions for each plant-unit combination
            descriptions = []

            for _, row in self.usa_details.iterrows():
                # Build comprehensive description for vector search
                desc_parts = []

                # Basic info
                desc_parts.append(f"Plant: {row['Plant Name']}")
                desc_parts.append(f"Unit: {row['Unit IDs']}")
                desc_parts.append(f"Operator: {row.get('Entity Name', 'Unknown')}")

                # Capacity info
                if pd.notna(row['Capacity']):
                    desc_parts.append(f"Capacity: {row['Capacity']} MW")

                # Plant type
                if pd.notna(row.get('Plant Type')):
                    desc_parts.append(f"Type: {row['Plant Type']}")

                # Operating info
                if pd.notna(row['Operating Year']):
                    desc_parts.append(f"Operating since: {row['Operating Year']}")

                # Retirement info
                if pd.notna(row.get('Planned Retirement Year')):
                    desc_parts.append(f"Retirement planned: {row['Planned Retirement Year']}")

                # Location info
                if pd.notna(row.get('Latitude')) and pd.notna(row.get('Longitude')):
                    desc_parts.append(f"Location: {row['Latitude']}, {row['Longitude']}")

                # Coal data if available
                plant_name = row['Plant Name']
                coal_matches = self.coal_data[self.coal_data['Plant Name'] == plant_name]
                if not coal_matches.empty:
                    fuel_types = coal_matches['Type'].unique()
                    desc_parts.append(f"Fuel types: {', '.join(fuel_types)}")

                    # Add emission factor info
                    years_with_data = coal_matches['Year'].unique()
                    desc_parts.append(f"Emission data available for: {', '.join(years_with_data)}")

                descriptions.append(" | ".join(desc_parts))

            self.plant_descriptions = descriptions

            # Create TF-IDF vectors
            self.vectorizer = TfidfVectorizer(
                max_features=1000,
                stop_words='english',
                ngram_range=(1, 2),
                lowercase=True
            )

            self.plant_vectors = self.vectorizer.fit_transform(descriptions)

            logger.info(f"Vector search initialized with {len(descriptions)} plant descriptions")

        except Exception as e:
            logger.error(f"Error initializing vector search: {e}")
            self.vectorizer = None
            self.plant_vectors = None

    def get_plant_data(self, plant_name: str, unit_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get comprehensive plant data for a specific plant and optionally unit.

        Args:
            plant_name: Name of the power plant
            unit_id: Optional unit identifier

        Returns:
            List of dictionaries containing plant/unit data in JSON schema format
        """
        results = []
        plant_name = normalize_plant_name(plant_name)

        # Filter USA Details data with smart matching
        # Try multiple search strategies to handle cases like "Cross Generating Station" vs "Cross"

        # Strategy 1: Exact match (case-insensitive)
        exact_filter = self.usa_details['Plant Name'].str.lower() == plant_name.lower()
        usa_matches = self.usa_details[exact_filter]

        # Strategy 2: If no exact match, try input contains Excel name (e.g., "Cross Generating Station" contains "Cross")
        if usa_matches.empty:
            contains_filter = self.usa_details['Plant Name'].apply(
                lambda excel_name: excel_name.lower() in plant_name.lower() if pd.notna(excel_name) else False
            )
            usa_matches = self.usa_details[contains_filter]

        # Strategy 3: If still no match, try Excel name contains input (original logic)
        if usa_matches.empty:
            original_filter = self.usa_details['Plant Name'].str.contains(plant_name, case=False, na=False)
            usa_matches = self.usa_details[original_filter]

        # Strategy 4: Fuzzy matching (handle periods, spaces, etc.)
        if usa_matches.empty:
            # Normalize both input and Excel names for fuzzy matching
            clean_input = plant_name.replace('.', '').replace(' ', '').lower()

            def fuzzy_match(excel_name):
                if pd.isna(excel_name):
                    return False
                clean_excel = str(excel_name).replace('.', '').replace(' ', '').lower()
                return clean_input in clean_excel or clean_excel in clean_input

            fuzzy_filter = self.usa_details['Plant Name'].apply(fuzzy_match)
            usa_matches = self.usa_details[fuzzy_filter]

            if not usa_matches.empty:
                logger.info(f"Found {len(usa_matches)} matches using fuzzy matching")

        # Apply unit filter if specified
        if unit_id and not usa_matches.empty:
            unit_filter = usa_matches['Unit IDs'].astype(str) == str(unit_id)
            usa_matches = usa_matches[unit_filter]

        logger.info(f"Plant search for '{plant_name}': Found {len(usa_matches)} matches using smart matching")

        for _, usa_row in usa_matches.iterrows():
            # Get yearly fuel data for this plant (currently only coal data available)
            exact_plant_name = usa_row['Plant Name']  # This is the CORRECT name from Excel
            plant_type = usa_row.get('Plant Type', '')

            # Try to find yearly fuel data (currently only coal sheets exist)
            yearly_fuel_matches = pd.DataFrame()  # Default to empty

            # Only look for coal data if it's a coal plant
            if 'coal' in plant_type.lower():
                # Strategy 1: Exact match with Excel plant name
                yearly_fuel_matches = self.coal_data[self.coal_data['Plant Name'] == exact_plant_name]

                # Strategy 2: If no exact match, try input contains coal plant name
                if yearly_fuel_matches.empty:
                    coal_contains_filter = self.coal_data['Plant Name'].apply(
                        lambda coal_name: coal_name.lower() in plant_name.lower() if pd.notna(coal_name) else False
                    )
                    yearly_fuel_matches = self.coal_data[coal_contains_filter]

                # Strategy 3: If still no match, try coal plant name contains input
                if yearly_fuel_matches.empty:
                    coal_filter = self.coal_data['Plant Name'].str.contains(
                        plant_name, case=False, na=False, regex=False
                    )
                    yearly_fuel_matches = self.coal_data[coal_filter]

            # For non-coal plants, yearly_fuel_matches will be empty (no yearly data available)
            # But we can still return basic plant info from USA Details

            # Build result according to JSON schema
            result = self._build_schema_response(usa_row, yearly_fuel_matches)
            results.append(result)

        return results

    def _build_schema_response(self, usa_row: pd.Series, yearly_fuel_data: pd.DataFrame) -> Dict[str, Any]:
        """
        Build response according to unit_level.json schema.
        UPDATED: Now handles all plant types, not just coal.

        Args:
            usa_row: Row from USA Details data
            yearly_fuel_data: Filtered yearly fuel data for the plant (empty for non-coal plants)

        Returns:
            Dictionary formatted according to JSON schema
        """
        # Clean plant and entity names for SK
        plant_name_clean = str(usa_row['Plant Name']).replace('#', '_').replace('"', '')
        entity_name_clean = str(usa_row.get('Entity Name', 'Unknown')).replace('#', '_').replace('"', '')
        plant_type_clean = str(usa_row.get('Plant Type', 'Unknown')).replace('#', '_').replace('"', '')

        # 🚨 CRITICAL FIX: Convert float Unit IDs (1.0) to clean integers (1)
        raw_unit_id = usa_row['Unit IDs']
        try:
            # Handle float unit IDs like 1.0 -> "1"
            clean_unit_id = str(int(float(raw_unit_id)))
        except (ValueError, TypeError):
            # For non-numeric unit IDs, keep as string
            clean_unit_id = str(raw_unit_id)

        # Base unit information
        response = {
            "sk": f"unit#{plant_type_clean}#{clean_unit_id}#{plant_name_clean}#{entity_name_clean}",
            "plant_name": str(usa_row['Plant Name']),  # Add plant_name field
            "unit_id": clean_unit_id,                  # 🚨 FIXED: Use clean unit ID
            "capacity": float(usa_row['Capacity']) if pd.notna(usa_row['Capacity']) else None,
            "capacity_unit": "MW",
            "commencement_date": usa_row['commencement_date'],
            "remaining_useful_life": usa_row['remaining_useful_life'],
            "unit_lifetime": usa_row['unit_lifetime'],
            "unit_number": clean_unit_id,              # 🚨 FIXED: Use clean unit ID
            "plant_id": plant_name_clean
        }

        # Add location data if available
        if pd.notna(usa_row.get('Latitude')) and pd.notna(usa_row.get('Longitude')):
            response["latitude"] = float(usa_row['Latitude'])
            response["longitude"] = float(usa_row['Longitude'])

        # Get plant type for fuel processing
        plant_type = usa_row.get('Plant Type', '')

        # Process fuel type and emission factor data from yearly fuel sheets
        if not yearly_fuel_data.empty:
            fuel_types = []
            emission_factors_dict = {}  # Use dict to avoid duplicates by year

            # 🚨 CRITICAL FIX: Order fuel types by total generation (highest first)
            # Calculate total generation for each fuel type to determine primary fuel
            fuel_type_totals = yearly_fuel_data.groupby('Type')['Net Generation (Megawatthours)'].sum().sort_values(ascending=False)

            # Group by fuel type in order of generation (highest first)
            for fuel_type in fuel_type_totals.index:
                group = yearly_fuel_data[yearly_fuel_data['Type'] == fuel_type]
                # Determine fuel name based on plant type
                fuel_name = "Coal"  # Default for coal plants
                if 'gas' in plant_type.lower():
                    fuel_name = "Natural Gas"
                elif 'nuclear' in plant_type.lower():
                    fuel_name = "Nuclear"
                elif 'hydro' in plant_type.lower():
                    fuel_name = "Hydro"
                elif 'wind' in plant_type.lower():
                    fuel_name = "Wind"
                elif 'solar' in plant_type.lower():
                    fuel_name = "Solar"

                # Build fuel type entry
                fuel_entry = {
                    "fuel": fuel_name,
                    "type": fuel_type,
                    "years_percentage": {}
                }

                # Add GCV value for coal types
                if fuel_name == "Coal":
                    from heat_rate_efficiency_calculator import HeatRateEfficiencyCalculator
                    heat_calc = HeatRateEfficiencyCalculator()
                    gcv_value = heat_calc.get_gcv_for_coal_type(fuel_type)
                    fuel_entry["gcv_coal"] = gcv_value

                # Calculate years_percentage based on actual generation data
                # First, get total generation by year for this plant to calculate percentages
                plant_yearly_totals = yearly_fuel_data.groupby('Year')['Net Generation (Megawatthours)'].sum()

                # Calculate actual percentage for each year based on generation data
                for _, row in group.iterrows():
                    year = str(row['Year'])
                    fuel_generation = row['Net Generation (Megawatthours)']
                    total_generation = plant_yearly_totals.get(row['Year'], 0)

                    if total_generation > 0:
                        percentage = fuel_generation / total_generation
                    else:
                        percentage = 0.0  # If no generation, percentage is 0

                    fuel_entry["years_percentage"][year] = percentage

                # 🚨 FIXED: Fill in missing years from commencement to unit lifetime end
                try:
                    commencement_date = usa_row['commencement_date']
                    commencement_year = int(commencement_date.split('-')[0])

                    # Get unit lifetime end year
                    unit_lifetime = usa_row.get('unit_lifetime')
                    if unit_lifetime and isinstance(unit_lifetime, str) and 'T' in unit_lifetime:
                        # Parse ISO timestamp to get year
                        lifetime_year = int(unit_lifetime.split('-')[0])
                    else:
                        # Fallback to current year + reasonable future projection
                        lifetime_year = 2050

                    logger.info(f"[Session {self.session_id}] Filling fuel percentages from {commencement_year} to {lifetime_year}")

                    # 🚨 CRITICAL FIX: Determine if this is the primary fuel type
                    # Primary fuel type = first in the generation-ordered list (highest total generation)
                    is_primary_fuel = (fuel_type == fuel_type_totals.index[0])
                    total_fuel_types = len(fuel_type_totals)

                    for year in range(commencement_year, lifetime_year + 1):
                        year_str = str(year)
                        if year_str not in fuel_entry["years_percentage"]:
                            # 🚨 SPECIAL HANDLING FOR REFINED COAL
                            if fuel_type == "Refined Coal":
                                # Refined coal should be ZERO except during actual generation years
                                fuel_entry["years_percentage"][year_str] = 0.0
                            elif total_fuel_types == 1:
                                # Only one fuel type - always 1.0
                                fuel_entry["years_percentage"][year_str] = 1.0
                            else:
                                # Multiple fuel types - primary gets 1.0, others get 0.0
                                if is_primary_fuel:
                                    fuel_entry["years_percentage"][year_str] = 1.0
                                else:
                                    fuel_entry["years_percentage"][year_str] = 0.0

                except Exception as e:
                    logger.warning(f"[Session {self.session_id}] Error filling fuel percentages: {e}")
                    # Fallback: ensure 2020-2050 are covered
                    for year in range(2020, 2051):
                        year_str = str(year)
                        if year_str not in fuel_entry["years_percentage"]:
                            # 🚨 SPECIAL HANDLING FOR REFINED COAL
                            if fuel_type == "Refined Coal":
                                # Refined coal should be ZERO except during actual generation years
                                fuel_entry["years_percentage"][year_str] = 0.0
                            else:
                                # Default to 1.0 if this appears to be the primary fuel
                                fuel_entry["years_percentage"][year_str] = 1.0 if len(fuel_types) == 0 else 0.0

                # 🚨 FIXED: Add yearly emission factor data (use highest value for duplicate years)
                for _, row in group.iterrows():
                    # Add emission factor entry
                    if pd.notna(row['Emission Factor']) and row['Emission Factor'] > 0:
                        year = int(row['Year'])
                        emission_factor = float(row['Emission Factor'])

                        # Always use the highest value for duplicate years
                        if year not in emission_factors_dict:
                            emission_factors_dict[year] = emission_factor
                        else:
                            # Keep the higher value
                            if emission_factor > emission_factors_dict[year]:
                                emission_factors_dict[year] = emission_factor
                                logger.info(f"[Session {self.session_id}] Updated emission factor for year {year}: {emission_factor} (higher than previous {emission_factors_dict[year]})")

                fuel_types.append(fuel_entry)

                # Get emission_factor_coal from Full Names mapping
                coal_type_normalized = fuel_type.lower().strip()
                for coal_type, factor in self.emission_factors_coal.items():
                    if coal_type.lower().strip() in coal_type_normalized or coal_type_normalized in coal_type.lower().strip():
                        response["emission_factor_coal"] = float(factor)
                        break

            # Convert emission factors dict to list format
            emission_factors = []
            for year in sorted(emission_factors_dict.keys(), reverse=True):  # Sort by year, newest first
                emission_factors.append({
                    "value": emission_factors_dict[year],
                    "year": year
                })

            if fuel_types:
                response["fuel_type"] = fuel_types
            if emission_factors:
                response["emission_factor"] = emission_factors

            # Calculate heat rate and efficiency using the new calculator
            self._add_heat_rate_and_efficiency(response, yearly_fuel_data, fuel_types)

            # CRITICAL FIX: Add PLF and auxiliary power calculations using USA Excel calculation engine
            self._add_usa_plf_calculations(response, yearly_fuel_data)

        else:
            # For non-coal plants (gas, nuclear, etc.) - provide basic fuel type info from plant type
            fuel_types = []

            # Determine fuel type from plant type
            if 'gas' in plant_type.lower():
                fuel_entry = {
                    "fuel": "Natural Gas",
                    "type": "Natural Gas",
                    "years_percentage": {}
                }
                # Set years_percentage from commencement to present
                try:
                    commencement_date = usa_row['commencement_date']
                    commencement_year = int(commencement_date.split('-')[0])
                    current_year = 2024
                    for year in range(commencement_year, current_year + 1):
                        fuel_entry["years_percentage"][str(year)] = 1.0
                except:
                    # Fallback: use 2020-2024
                    for year in range(2020, 2025):
                        fuel_entry["years_percentage"][str(year)] = 1.0

                fuel_types.append(fuel_entry)
                response["fuel_type"] = fuel_types

                # Set default emission factor for gas plants
                response["emission_factor_gas"] = 2.6928  # Default gas emission factor

            # Note: No yearly generation/fuel data available for non-coal plants
            # This is expected since only Coal yearly sheets exist in the Excel file

        # 🚨 FIXED: Set unit-level gcv_coal from primary fuel type (first in ordered list)
        fuel_types = response.get("fuel_type", [])
        if fuel_types:
            # Use the FIRST fuel type (which is now ordered by highest generation)
            primary_fuel = fuel_types[0]
            if primary_fuel.get("fuel") == "Coal" and "gcv_coal" in primary_fuel:
                primary_coal_gcv = primary_fuel["gcv_coal"]
                response["gcv_coal"] = primary_coal_gcv
                logger.info(f"[Session {self.session_id}] Set unit-level gcv_coal to {primary_coal_gcv} from primary fuel type: {primary_fuel.get('type', 'Unknown')}")
            else:
                logger.warning(f"[Session {self.session_id}] Primary fuel is not coal or missing gcv_coal: {primary_fuel}")
        else:
            logger.warning(f"[Session {self.session_id}] No fuel_type data found for unit-level gcv_coal")

        return response

    def _add_heat_rate_and_efficiency(self, response: Dict[str, Any], yearly_fuel_data: pd.DataFrame, fuel_types: List[Dict]) -> None:
        """
        Add heat rate and efficiency calculations to the response.
        UPDATED: Now handles yearly fuel data (currently only coal data available).

        Args:
            response: Response dictionary to update
            yearly_fuel_data: Yearly fuel data for the plant (consolidated data)
            fuel_types: List of fuel type information
        """
        try:
            plant_name = response.get('plant_name', '')
            if not plant_name:
                logger.warning(f"[Session {self.session_id}] No plant name available for heat rate calculations")
                return

            # Get raw coal data from individual year sheets (not the consolidated data)
            raw_coal_data = self._get_raw_coal_data_for_plant(plant_name)

            if raw_coal_data.empty:
                logger.warning(f"[Session {self.session_id}] No raw coal data found for {plant_name}")
                return

            # Filter to get the row with the highest net generation (to avoid 0 values)
            net_gen_col = 'Net Generation (Megawatthours)'
            coal_data_with_generation = raw_coal_data[raw_coal_data[net_gen_col] > 0]

            if coal_data_with_generation.empty:
                logger.warning(f"[Session {self.session_id}] No coal data with generation > 0 found for {plant_name}")
                return

            # 🚨 FIXED: Use 3-year average and correct column name
            # Get the most recent 3 years with actual generation data
            recent_years = coal_data_with_generation.nlargest(3, 'Year')

            if len(recent_years) == 0:
                logger.warning(f"[Session {self.session_id}] No recent years data found for heat rate calculation")
                return

            # Calculate 3-year averages using correct column names
            avg_net_generation_mwh = recent_years[net_gen_col].mean()
            avg_fuel_consumed_tons = recent_years['Total Fuel Consumed Tons'].mean()  # ✅ CORRECT: Total Fuel Consumed Tons

            logger.info(f"[Session {self.session_id}] Using 3-year average from years: {recent_years['Year'].tolist()}")
            logger.info(f"[Session {self.session_id}] Average net generation: {avg_net_generation_mwh:.2f} MWh")
            logger.info(f"[Session {self.session_id}] Average fuel consumed: {avg_fuel_consumed_tons:.2f} tons")

            # Use averaged values for calculations
            net_generation_mwh = avg_net_generation_mwh
            fuel_consumed_tons = avg_fuel_consumed_tons

            # Get coal type from fuel_types
            coal_type = fuel_types[0].get('type', 'bituminous') if fuel_types else 'bituminous'

            # Get capacity and technology for auxiliary power calculation
            capacity = response.get('capacity', 1000)
            technology = 'subcritical'  # Default for coal plants

            # 🚨 CRITICAL FIX: Get auxiliary power based on capacity and technology (NOT HARDCODED!)
            # Import and initialize the USA calculation engine for auxiliary power
            from usa_excel_calculation_engine import create_usa_excel_calculation_engine
            temp_usa_calc_engine = create_usa_excel_calculation_engine()
            auxiliary_power_decimal = temp_usa_calc_engine.get_auxiliary_power_percentage(capacity, technology)

            logger.info(f"[Session {self.session_id}] Heat rate calculation inputs:")
            logger.info(f"[Session {self.session_id}]   Net generation: {net_generation_mwh} MWh")
            logger.info(f"[Session {self.session_id}]   Fuel consumed: {fuel_consumed_tons} tons")
            logger.info(f"[Session {self.session_id}]   Coal type: {coal_type}")
            logger.info(f"[Session {self.session_id}]   Capacity: {capacity} MW")

            # Validate data
            if net_generation_mwh <= 0 or fuel_consumed_tons <= 0:
                logger.warning(f"[Session {self.session_id}] Invalid data for calculations: net_gen={net_generation_mwh}, fuel={fuel_consumed_tons}")
                return

            # Calculate heat rate and efficiency
            heat_rate, efficiency = self.heat_rate_calculator.calculate_heat_rate_and_efficiency(
                net_generation_mwh=float(net_generation_mwh),
                fuel_consumed_tons=float(fuel_consumed_tons),
                coal_type=coal_type,
                auxiliary_power_decimal=auxiliary_power_decimal
            )

            # Add to response
            response["coal_unit_efficiency"] = round(efficiency, 4)  # Store as decimal
            response["heat_rate_kcal_per_kwh"] = round(heat_rate, 2)
            response["heat_rate"] = round(heat_rate, 2)  # Also store in standard field

            logger.info(f"[Session {self.session_id}] ✅ Added heat rate and efficiency:")
            logger.info(f"[Session {self.session_id}]   Efficiency: {efficiency:.4f} ({efficiency*100:.2f}%)")
            logger.info(f"[Session {self.session_id}]   Heat Rate: {heat_rate:.2f} kCal/kWh")

        except Exception as e:
            logger.error(f"[Session {self.session_id}] Error calculating heat rate and efficiency: {e}")

    def _add_usa_plf_calculations(self, response: Dict[str, Any], yearly_fuel_data: pd.DataFrame) -> None:
        """
        Add PLF and auxiliary power calculations using the USA Excel calculation engine.

        Args:
            response: Response dictionary to update
            yearly_fuel_data: Yearly fuel data for the plant
        """
        try:
            plant_name = response.get('plant_name', '')
            if not plant_name:
                logger.warning(f"[Session {self.session_id}] No plant name available for PLF calculations")
                return

            # Import and initialize the USA calculation engine
            from usa_excel_calculation_engine import create_usa_excel_calculation_engine

            # FIXED: Use factory function with correct absolute paths
            usa_calc_engine = create_usa_excel_calculation_engine()

            # CRITICAL FIX: Use the EXACT plant name from Excel, not the input name
            exact_plant_name = response.get('plant_name', plant_name)  # Use matched name from Excel

            # Prepare unit data for the calculation engine
            unit_data = {
                'capacity': response.get('capacity', 500),  # MW
                'unit_number': response.get('unit_id', '1'),
                'technology': 'subcritical',  # Default for coal plants
                'plant_name': exact_plant_name  # Use exact Excel name
            }

            logger.info(f"[Session {self.session_id}] 🔧 Starting USA PLF calculations for {exact_plant_name}")

            # Calculate all USA parameters (PLF, auxiliary power, gross generation)
            usa_calculations = usa_calc_engine.calculate_unit_parameters_usa(exact_plant_name, unit_data)

            if usa_calculations:
                logger.info(f"[Session {self.session_id}] ✅ USA PLF calculations successful: {len(usa_calculations)} fields")

                # Add the calculated fields to the response
                for field, value in usa_calculations.items():
                    if value is not None:
                        response[field] = value
                        logger.info(f"[Session {self.session_id}]   Added {field}: {value}")
            else:
                logger.warning(f"[Session {self.session_id}] ⚠️ USA PLF calculations returned no results")

        except Exception as e:
            logger.error(f"[Session {self.session_id}] ❌ Error in USA PLF calculations: {e}")

    def _get_raw_coal_data_for_plant(self, plant_name: str) -> pd.DataFrame:
        """
        Get raw coal data from individual year sheets for a specific plant.

        Args:
            plant_name: Name of the plant

        Returns:
            DataFrame with raw coal data including generation and fuel consumption
        """
        try:
            all_raw_data = []

            # Load data from individual coal year sheets
            coal_years = ['Coal 2024', 'Coal 2023', 'Coal 2022', 'Coal 2021', 'Coal 2020']

            for year_sheet in coal_years:
                try:
                    # Load the specific year sheet
                    year_data = pd.read_excel(self.excel_file_path, sheet_name=year_sheet)

                    # Filter for the specific plant
                    plant_data = year_data[year_data['Plant Name'].str.contains(plant_name, case=False, na=False)]

                    if not plant_data.empty:
                        # Add year column
                        year = int(year_sheet.split()[-1])  # Extract year from sheet name
                        plant_data = plant_data.copy()
                        plant_data['Year'] = year
                        all_raw_data.append(plant_data)

                        logger.info(f"[Session {self.session_id}] Found {len(plant_data)} records for {plant_name} in {year_sheet}")

                except Exception as e:
                    logger.warning(f"[Session {self.session_id}] Could not load {year_sheet}: {e}")
                    continue

            if all_raw_data:
                # Combine all years data
                combined_data = pd.concat(all_raw_data, ignore_index=True)
                logger.info(f"[Session {self.session_id}] Combined {len(combined_data)} total records for {plant_name}")
                return combined_data
            else:
                logger.warning(f"[Session {self.session_id}] No raw coal data found for {plant_name}")
                return pd.DataFrame()

        except Exception as e:
            logger.error(f"[Session {self.session_id}] Error loading raw coal data: {e}")
            return pd.DataFrame()

    def query(self, natural_language_query: str) -> List[Dict[str, Any]]:
        """
        Process natural language query and return structured results.

        Args:
            natural_language_query: Natural language query about power plants

        Returns:
            List of results formatted according to JSON schema
        """
        logger.info(f"Processing query: {natural_language_query}")

        query_lower = natural_language_query.lower()
        results = []

        # Try vector search first if available
        if self.vectorizer is not None and self.plant_vectors is not None:
            vector_results = self._vector_search(natural_language_query)
            if vector_results:
                results.extend(vector_results)
                logger.info(f"Found {len(results)} results using vector search")
                return results

        # Fallback to keyword-based search
        # Extract plant name if mentioned
        plant_names = self._extract_plant_names(query_lower)

        # Extract unit information if mentioned
        unit_info = self._extract_unit_info(query_lower)

        if plant_names:
            # Search for specific plants
            for plant_name in plant_names:
                plant_results = self.get_plant_data(plant_name, unit_info.get('unit_id'))
                results.extend(plant_results)
        else:
            # Broader search based on query content
            results = self._broad_search(query_lower)

        logger.info(f"Found {len(results)} results for query")
        return results

    def _vector_search(self, query: str, top_k: int = 10) -> List[Dict[str, Any]]:
        """
        Perform vector-based semantic search.

        Args:
            query: Natural language query
            top_k: Number of top results to return

        Returns:
            List of matching results
        """
        try:
            # Transform query to vector
            query_vector = self.vectorizer.transform([query])

            # Calculate similarities
            similarities = cosine_similarity(query_vector, self.plant_vectors).flatten()

            # Get top matches
            top_indices = np.argsort(similarities)[::-1][:top_k]

            # Filter out very low similarity scores
            min_similarity = 0.1
            top_indices = [idx for idx in top_indices if similarities[idx] > min_similarity]

            results = []
            for idx in top_indices:
                # Get the corresponding row from USA details
                usa_row = self.usa_details.iloc[idx]

                # Get coal data for this plant
                plant_name = usa_row['Plant Name']
                coal_matches = self.coal_data[self.coal_data['Plant Name'] == plant_name]

                # If no exact match, try fuzzy matching
                if coal_matches.empty:
                    coal_filter = self.coal_data['Plant Name'].str.contains(
                        plant_name, case=False, na=False, regex=False
                    )
                    coal_matches = self.coal_data[coal_filter]

                # Build result
                result = self._build_schema_response(usa_row, coal_matches)
                result['_similarity_score'] = float(similarities[idx])  # Add for debugging
                results.append(result)

            return results

        except Exception as e:
            logger.error(f"Vector search failed: {e}")
            return []

    def _extract_plant_names(self, query: str) -> List[str]:
        """
        Extract plant names mentioned in the query.

        Args:
            query: Lowercase query string

        Returns:
            List of plant names found in query
        """
        plant_names = []

        # Get unique plant names from our data
        unique_plants = self.usa_details['Plant Name'].unique()

        for plant in unique_plants:
            if pd.notna(plant) and plant.lower() in query:
                plant_names.append(plant)

        return plant_names

    def _extract_unit_info(self, query: str) -> Dict[str, Any]:
        """
        Extract unit information from query.

        Args:
            query: Lowercase query string

        Returns:
            Dictionary with unit information
        """
        unit_info = {}

        # Look for unit patterns
        import re
        unit_patterns = [
            r'unit\s+(\d+)',
            r'unit\s+([a-zA-Z0-9]+)',
            r'unit\s+id\s+(\d+)',
            r'unit\s+([a-zA-Z]+\d+)'
        ]

        for pattern in unit_patterns:
            match = re.search(pattern, query)
            if match:
                unit_info['unit_id'] = match.group(1)
                break

        return unit_info

    def _broad_search(self, query: str) -> List[Dict[str, Any]]:
        """
        Perform broader search based on query keywords.

        Args:
            query: Lowercase query string

        Returns:
            List of matching results
        """
        results = []

        # Search criteria based on keywords
        search_filters = []

        # Fuel type filters
        if 'coal' in query:
            search_filters.append(('Plant Type', 'coal'))
        if 'gas' in query or 'natural gas' in query:
            search_filters.append(('Plant Type', 'gas'))
        if 'nuclear' in query:
            search_filters.append(('Plant Type', 'nuclear'))

        # Capacity filters
        if 'capacity' in query:
            # Return plants with capacity data
            capacity_filter = self.usa_details['Capacity'].notna()
            filtered_data = self.usa_details[capacity_filter]
        else:
            filtered_data = self.usa_details

        # Apply search filters
        for filter_col, filter_val in search_filters:
            if filter_col in filtered_data.columns:
                filtered_data = filtered_data[
                    filtered_data[filter_col].str.contains(filter_val, case=False, na=False)
                ]

        # Limit results to prevent overwhelming response
        limited_data = filtered_data.head(10)

        for _, row in limited_data.iterrows():
            plant_name = row['Plant Name']
            unit_id = row['Unit IDs']

            # Get coal data for this plant - try exact match first
            coal_matches = self.coal_data[self.coal_data['Plant Name'] == plant_name]

            # If no exact match, try fuzzy matching
            if coal_matches.empty:
                coal_filter = self.coal_data['Plant Name'].str.contains(
                    plant_name, case=False, na=False, regex=False
                )
                coal_matches = self.coal_data[coal_filter]

            # Build result
            result = self._build_schema_response(row, coal_matches)
            results.append(result)

        return results

    def get_summary_stats(self) -> Dict[str, Any]:
        """
        Get summary statistics about the loaded data.

        Returns:
            Dictionary with summary statistics
        """
        stats = {
            "total_units": len(self.usa_details) if self.usa_details is not None else 0,
            "total_plants": len(self.usa_details['Plant Name'].unique()) if self.usa_details is not None else 0,
            "coal_records": len(self.coal_data) if self.coal_data is not None else 0,
            "emission_factor_mappings": len(self.emission_factors_coal) if self.emission_factors_coal else 0,
            "years_covered": sorted(self.coal_data['Year'].unique().tolist()) if self.coal_data is not None and not self.coal_data.empty else []
        }

        if self.usa_details is not None:
            stats["capacity_range"] = {
                "min": float(self.usa_details['Capacity'].min()),
                "max": float(self.usa_details['Capacity'].max()),
                "mean": float(self.usa_details['Capacity'].mean())
            }

        return stats
