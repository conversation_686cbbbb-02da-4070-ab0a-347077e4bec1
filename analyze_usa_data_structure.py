#!/usr/bin/env python3
"""
Analyze the USA Details.xlsx data structure to understand if it's plant-level or unit-level data
"""

import pandas as pd

def analyze_usa_data_structure():
    """Analyze the structure of USA data to determine if it's plant-level or unit-level"""
    
    print("🔍 ANALYZING USA DETAILS.XLSX DATA STRUCTURE")
    print("=" * 60)
    
    excel_file = '/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH-feat-agi-tech-int-007-feat-agi-tech-int-007/USA Details.xlsx'
    
    # First check the USA Details sheet for unit information
    print("📊 CHECKING USA DETAILS SHEET FOR UNIT STRUCTURE:")
    print("-" * 50)
    
    usa_df = pd.read_excel(excel_file, sheet_name='USA Details')
    antelope_mask = usa_df['Plant Name'].str.contains('Antelope Valley', na=False, case=False)
    antelope_data = usa_df[antelope_mask]
    
    print(f"✅ Found {len(antelope_data)} records for Antelope Valley in USA Details sheet")
    
    for idx, row in antelope_data.iterrows():
        print(f"\nRecord {idx}:")
        print(f"  Plant Name: {row['Plant Name']}")
        print(f"  Unit IDs: {row['Unit IDs']}")
        print(f"  Capacity: {row['Capacity']} MW")
        print(f"  Operating Year: {row['Operating Year']}")
    
    print(f"\n📊 CHECKING COAL YEARLY SHEETS FOR GENERATION DATA:")
    print("-" * 50)
    
    coal_sheets = ['Coal 2024', 'Coal 2023', 'Coal 2022', 'Coal 2021', 'Coal 2020']
    
    for sheet_name in coal_sheets:
        year = sheet_name.split(' ')[1]
        df = pd.read_excel(excel_file, sheet_name=sheet_name)
        
        # Find Antelope Valley data
        antelope_mask = df['Plant Name'].str.contains('Antelope Valley', na=False, case=False)
        antelope_data = df[antelope_mask]
        
        if not antelope_data.empty:
            print(f"\n📅 {year} - Found {len(antelope_data)} records:")
            
            total_net_generation = 0
            for idx, row in antelope_data.iterrows():
                net_gen = row.get('Net Generation (Megawatthours)', 0)
                total_net_generation += net_gen
                print(f"  Record {idx}: {net_gen:,.0f} MWh")
            
            print(f"  📊 Total Net Generation for {year}: {total_net_generation:,.0f} MWh")
    
    print(f"\n🤔 ANALYSIS: PLANT-LEVEL vs UNIT-LEVEL DATA")
    print("-" * 50)
    
    print("EVIDENCE FOR PLANT-LEVEL DATA:")
    print("1. ✅ USA Details shows 2 separate units (Unit 1 and Unit 2)")
    print("2. ✅ Each unit has 477 MW capacity")
    print("3. ✅ Coal yearly sheets show 2 records per year for Antelope Valley")
    print("4. ✅ The 2 records likely represent the TOTAL plant generation split by fuel type")
    print("5. ✅ One record is Lignite, one is Refined Coal - both are plant-level totals")
    
    print("\nEVIDENCE AGAINST UNIT-LEVEL DATA:")
    print("1. ❌ No clear unit identification in Coal yearly sheets")
    print("2. ❌ Records are differentiated by coal type, not unit number")
    print("3. ❌ Generation values are very high for individual units")
    
    print(f"\n🎯 CONCLUSION:")
    print("-" * 20)
    print("The Net Generation data in Coal yearly sheets appears to be PLANT-LEVEL data")
    print("split by fuel type (Lignite vs Refined Coal), NOT unit-level data.")
    print()
    print("Therefore, we should use:")
    print("✅ CASE 1: Plant-level gross generation → extrapolate to unit level")
    print("✅ Step 1: Convert Net to Gross using Case 4a")
    print("✅ Step 2: Calculate plant-level PLF using Case 1 formula")
    print("✅ Step 3: Extrapolate to unit-level using Case 1 method")
    
    print(f"\n📋 CORRECT APPROACH FOR USA PLANTS:")
    print("-" * 40)
    print("1. Sum the Net Generation from both fuel types (Lignite + Refined Coal)")
    print("2. Convert to Gross: Gross_Plant = Net_Plant / (1 - AUX%)")
    print("3. Calculate Plant PLF: PLF_plant = Gross_Plant / (Plant_Capacity_MW * 8760 * PAF)")
    print("4. Calculate Unit Generation: Gen_unit = Unit_Capacity_MW * PLF_plant * PAF_unit")
    print("5. Calculate Unit PLF: PLF_unit = Gen_unit / (Unit_Capacity_MW * 8760 * PAF_unit)")
    
    print(f"\n🔧 VARIABLES NEEDED:")
    print("-" * 20)
    print("• Net_Plant_Generation = Sum of both fuel type records")
    print("• Plant_Capacity_MW = 954 MW (2 units × 477 MW)")
    print("• Unit_Capacity_MW = 477 MW")
    print("• AUX% = 7% (from Assumptions sheet)")
    print("• PAF = 1.0 (assumed if not available)")

if __name__ == "__main__":
    analyze_usa_data_structure()
