#!/usr/bin/env python3
"""
Debug script to find why remaining_useful_life is null
"""

import sys
import pandas as pd
from datetime import datetime, timed<PERSON><PERSON>

# Add the project root to Python path
sys.path.append('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH-feat-agi-tech-int-007')

def debug_remaining_life():
    """Debug the remaining useful life calculation"""
    
    print("🔍 DEBUGGING REMAINING USEFUL LIFE CALCULATION")
    print("=" * 60)
    
    try:
        from excel_power_plant_tool import ExcelPowerPlantTool
        
        # Create tool instance
        excel_tool = ExcelPowerPlantTool('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH-feat-agi-tech-int-007/USA Details.xlsx', 'debug')
        
        # Read the raw Excel data
        print("📊 Reading raw Excel data...")
        df = pd.read_excel('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH-feat-agi-tech-int-007/USA Details.xlsx', sheet_name='USA Details')
        
        # Filter for Cardinal
        cardinal_data = df[df['Plant Name'] == 'Cardinal']

        if cardinal_data.empty:
            print("❌ No Cardinal data found")
            return

        print(f"✅ Found {len(cardinal_data)} units for Cardinal")
        
        # Check the first unit
        first_unit = cardinal_data.iloc[0]
        
        print(f"\n📋 RAW EXCEL DATA:")
        print(f"Plant Name: {first_unit['Plant Name']}")
        print(f"Unit IDs: {first_unit['Unit IDs']}")
        print(f"Commencement Date: {first_unit['Commencement Date']}")
        print(f"Planned Retirement Year: {first_unit.get('Planned Retirement Year', 'N/A')}")
        print(f"Planned Retirement Month: {first_unit.get('Planned Retirement Month', 'N/A')}")
        
        # Test the commencement date conversion
        print(f"\n🔧 TESTING COMMENCEMENT DATE CONVERSION:")
        try:
            comm_date_raw = first_unit['Commencement Date']
            print(f"Raw commencement date: {comm_date_raw} (type: {type(comm_date_raw)})")
            
            # Convert to ISO format (same as Excel tool)
            if isinstance(comm_date_raw, str):
                comm_date_dt = datetime.strptime(comm_date_raw, '%Y-%m-%d')
            else:
                comm_date_dt = comm_date_raw
            
            comm_date_iso = comm_date_dt.strftime('%Y-%m-%dT%H:%M:%S.%f')[:-3] + 'Z'
            print(f"Converted to ISO: {comm_date_iso}")
            
        except Exception as e:
            print(f"❌ Error converting commencement date: {e}")
            return
        
        # Test the remaining useful life calculation manually
        print(f"\n🔧 TESTING REMAINING USEFUL LIFE CALCULATION:")
        try:
            # Create a test row
            test_row = {
                'commencement_date': comm_date_iso,
                'Planned Retirement Year': first_unit.get('Planned Retirement Year'),
                'Planned Retirement Month': first_unit.get('Planned Retirement Month', 1)
            }
            
            print(f"Test row: {test_row}")
            
            # Manual calculation
            commencement_date = datetime.strptime(test_row['commencement_date'], '%Y-%m-%dT%H:%M:%S.%fZ')
            print(f"Parsed commencement date: {commencement_date}")
            
            # Check for planned retirement
            if pd.notna(test_row['Planned Retirement Year']):
                retirement_year = int(test_row['Planned Retirement Year'])
                retirement_month = int(test_row.get('Planned Retirement Month', 1))
                retirement_date = datetime(retirement_year, retirement_month, 1)
                print(f"Using planned retirement: {retirement_date}")
            else:
                # Add 50 years to commencement
                retirement_date = commencement_date + timedelta(days=50 * 365.25)
                print(f"Using 50-year rule: {retirement_date}")
            
            result = retirement_date.strftime('%Y-%m-%dT%H:%M:%S.%f')[:-3] + 'Z'
            print(f"✅ Manual calculation result: {result}")
            
            # Test the Excel tool method
            print(f"\n🔧 TESTING EXCEL TOOL METHOD:")
            calculated_result = excel_tool._calculate_remaining_useful_life(test_row)
            print(f"Excel tool result: {calculated_result}")
            
            if calculated_result == result:
                print("✅ Excel tool calculation matches manual calculation")
            else:
                print("❌ Excel tool calculation differs from manual calculation")
                
        except Exception as e:
            print(f"❌ Error in manual calculation: {e}")
            import traceback
            traceback.print_exc()
        
        # Test the full plant data extraction
        print(f"\n🔧 TESTING FULL PLANT DATA EXTRACTION:")
        try:
            plant_data = excel_tool.get_plant_data('Cardinal')
            
            if plant_data:
                unit = plant_data[0]
                print(f"Extracted unit data:")
                print(f"  commencement_date: {unit.get('commencement_date')}")
                print(f"  unit_lifetime: {unit.get('unit_lifetime')}")
                print(f"  remaining_useful_life: {unit.get('remaining_useful_life')}")
                
                if unit.get('remaining_useful_life') is None:
                    print("❌ remaining_useful_life is None in extracted data")
                else:
                    print("✅ remaining_useful_life is present in extracted data")
            else:
                print("❌ No plant data extracted")
                
        except Exception as e:
            print(f"❌ Error in plant data extraction: {e}")
            import traceback
            traceback.print_exc()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_remaining_life()
