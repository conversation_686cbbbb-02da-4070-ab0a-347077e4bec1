#!/usr/bin/env python3
"""
Comprehensive Plant Calculations Test
====================================

Test ALL calculations for specific plants with detailed breakdown:
1. Antelope Valley
2. Dry Fork  
3. Laramie River

For each plant, show:
- Capacities (unit and plant)
- Fuel types and technologies
- Auxiliary power calculation
- GCV values
- Net generation from Excel
- Emission factors
- Gross generation calculation
- PLF calculation
- Fuel percentages

This will verify that all formulas and data extraction are working correctly.
"""

import sys
import os
sys.path.append('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH-feat-agi-tech-int-007')

from usa_excel_calculation_engine import USAExcelCalculationEngine
from excel_power_plant_tool import ExcelPowerPlantTool
import pandas as pd
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def test_comprehensive_plant_calculations(plant_name: str):
    """Test comprehensive calculations for a specific plant"""
    print(f"\n{'='*100}")
    print(f"🔍 COMPREHENSIVE CALCULATIONS TEST: {plant_name}")
    print(f"{'='*100}")
    
    try:
        # Initialize engines
        usa_engine = USAExcelCalculationEngine(
            '/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH-feat-agi-tech-int-007/USA Details.xlsx',
            '/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH-feat-agi-tech-int-007/current state calculations.xlsx'
        )
        
        excel_tool = ExcelPowerPlantTool('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH-feat-agi-tech-int-007/USA Details.xlsx', 'test')
        
        print(f"\n📊 STEP 1: BASIC PLANT INFORMATION")
        print("-" * 50)
        
        # 1. Get plant capacity from USA Details
        plant_capacity = usa_engine._calculate_plant_capacity_from_excel(plant_name)
        print(f"Total Plant Capacity: {plant_capacity} MW")
        
        # 2. Get unit information from Excel tool
        unit_results = excel_tool.get_plant_data(plant_name)
        if unit_results:
            print(f"Number of Units: {len(unit_results)}")
            for i, unit in enumerate(unit_results, 1):
                unit_id = unit.get('unit_id', 'N/A')
                capacity = unit.get('capacity', 'N/A')
                technology = unit.get('technology', 'N/A')
                print(f"  Unit {i}: ID={unit_id}, Capacity={capacity} MW, Technology={technology}")
        else:
            print("❌ No unit data found")
            return False
            
        print(f"\n📊 STEP 2: FUEL TYPES AND TECHNOLOGIES")
        print("-" * 50)
        
        # Get fuel type information from Excel data
        plant_excel_data = usa_engine.extract_plant_data_from_usa_excel(plant_name)
        if plant_excel_data:
            # Check 2024 data for fuel types
            if '2024' in plant_excel_data:
                year_data = plant_excel_data['2024']
                if isinstance(year_data, list) and year_data:
                    fuel_types = set()
                    for record in year_data:
                        fuel = record.get('Fuel', 'Unknown')
                        fuel_types.add(fuel)
                    print(f"Fuel Types in 2024: {list(fuel_types)}")
                else:
                    print("No 2024 fuel data found")
            else:
                print("No 2024 data available")
        else:
            print("❌ No Excel plant data found")
            
        print(f"\n📊 STEP 3: AUXILIARY POWER CALCULATION")
        print("-" * 50)
        
        # Test auxiliary power calculation for first unit
        if unit_results:
            first_unit = unit_results[0]
            unit_capacity = first_unit.get('capacity', 0)
            technology = first_unit.get('technology', 'subcritical')
            
            aux_power = usa_engine.get_auxiliary_power_percentage(unit_capacity, technology)
            print(f"Unit Capacity: {unit_capacity} MW")
            print(f"Technology: {technology}")
            print(f"Auxiliary Power: {aux_power*100:.1f}% (decimal: {aux_power})")
            
            # Show the logic
            if unit_capacity > 1000:
                expected_aux = "7% (>1000 MW)"
            elif unit_capacity > 750:
                expected_aux = "8% (750-1000 MW)"
            elif unit_capacity > 500:
                expected_aux = "9% (500-750 MW)"
            else:
                expected_aux = "10-11% (<500 MW)"
            print(f"Expected Range: {expected_aux}")
            
        print(f"\n📊 STEP 4: GCV VALUES")
        print("-" * 50)
        
        # Test GCV calculation for different coal types
        if plant_excel_data and '2024' in plant_excel_data:
            year_data = plant_excel_data['2024']
            if isinstance(year_data, list) and year_data:
                for record in year_data:
                    fuel = record.get('Fuel', 'Unknown')
                    if 'coal' in fuel.lower():
                        gcv = usa_engine.get_gcv_for_coal_type(fuel)
                        print(f"Fuel: {fuel} → GCV: {gcv} kcal/kg")
                        
        # Also test standard coal types
        standard_coals = ['bituminous', 'sub-bituminous', 'lignite']
        for coal_type in standard_coals:
            gcv = usa_engine.get_gcv_for_coal_type(coal_type)
            print(f"Standard {coal_type}: {gcv} kcal/kg")
            
        print(f"\n📊 STEP 5: NET GENERATION FROM EXCEL")
        print("-" * 50)
        
        # Extract net generation data from Excel
        if plant_excel_data:
            for year in ['2024', '2023', '2022']:
                if year in plant_excel_data:
                    year_data = plant_excel_data[year]
                    if isinstance(year_data, list):
                        total_net_gen = 0
                        for record in year_data:
                            net_gen = record.get('Net Generation (Megawatthours)', 0)
                            if isinstance(net_gen, (int, float)):
                                total_net_gen += net_gen
                        print(f"{year}: Net Generation = {total_net_gen:,.0f} MWh")
                        
        return True
        
    except Exception as e:
        print(f"❌ Error testing {plant_name}: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Test all specified plants"""
    print("🚀 COMPREHENSIVE PLANT CALCULATIONS TEST")
    print("=" * 100)
    print("Testing detailed calculations for:")
    print("1. Antelope Valley")
    print("2. Dry Fork") 
    print("3. Laramie River")
    print()
    print("For each plant, we will verify:")
    print("- Capacities (unit and plant)")
    print("- Fuel types and technologies") 
    print("- Auxiliary power calculation")
    print("- GCV values")
    print("- Net generation from Excel")
    print("- Emission factors")
    print("- Gross generation calculation")
    print("- PLF calculation")
    print("- Fuel percentages")
    
    test_plants = [
        "Antelope Valley",
        "Dry Fork",
        "Laramie River"
    ]
    
    results = {}
    
    for plant_name in test_plants:
        success = test_comprehensive_plant_calculations(plant_name)
        results[plant_name] = success
    
    # Summary
    print(f"\n{'='*100}")
    print("🎯 COMPREHENSIVE TEST SUMMARY")
    print(f"{'='*100}")
    
    for plant_name, success in results.items():
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{status}: {plant_name}")
    
    total_passed = sum(results.values())
    total_tests = len(results)
    
    print(f"\nOverall: {total_passed}/{total_tests} plants tested successfully")
    
    if total_passed == total_tests:
        print("🎉 ALL COMPREHENSIVE TESTS PASSED!")
    else:
        print("⚠️ Some tests failed - Check calculations and data extraction")

if __name__ == "__main__":
    main()
