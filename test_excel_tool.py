#!/usr/bin/env python3
"""
Test script for the Excel Power Plant Tool.
Tests data loading, processing, and query functionality.
"""

import json
from excel_power_plant_tool import ExcelPowerPlantTool

def test_data_loading():
    """Test basic data loading and processing."""
    print("=" * 50)
    print("Testing Excel Power Plant Tool Data Loading")
    print("=" * 50)
    
    try:
        # Initialize the tool
        tool = ExcelPowerPlantTool()
        
        # Get summary statistics
        stats = tool.get_summary_stats()
        print("\nData Summary:")
        print(f"Total Units: {stats['total_units']}")
        print(f"Total Plants: {stats['total_plants']}")
        print(f"Coal Records: {stats['coal_records']}")
        print(f"Emission Factor Mappings: {stats['emission_factor_mappings']}")
        print(f"Years Covered: {stats['years_covered']}")
        
        if 'capacity_range' in stats:
            print(f"Capacity Range: {stats['capacity_range']['min']:.1f} - {stats['capacity_range']['max']:.1f} MW")
            print(f"Average Capacity: {stats['capacity_range']['mean']:.1f} MW")
        
        return tool
        
    except Exception as e:
        print(f"Error during data loading: {e}")
        return None

def test_specific_plant_query(tool):
    """Test querying for a specific plant."""
    print("\n" + "=" * 50)
    print("Testing Specific Plant Query")
    print("=" * 50)
    
    # Test with A B Brown plant (we saw this in the data)
    plant_name = "A B Brown"
    print(f"\nQuerying for plant: {plant_name}")
    
    results = tool.get_plant_data(plant_name)
    
    if results:
        print(f"Found {len(results)} units for {plant_name}")
        
        # Display first result in detail
        first_result = results[0]
        print("\nFirst Unit Details:")
        print(f"  Unit Number: {first_result.get('unit_number')}")
        print(f"  Capacity: {first_result.get('capacity')} {first_result.get('capacity_unit')}")
        print(f"  Commencement Date: {first_result.get('commencement_date')}")
        print(f"  Unit Lifetime: {first_result.get('unit_lifetime')} years")
        
        if 'fuel_type' in first_result:
            print(f"  Fuel Types: {len(first_result['fuel_type'])}")
            for fuel in first_result['fuel_type']:
                print(f"    - {fuel['fuel']} ({fuel['type']})")
        
        if 'emission_factor' in first_result:
            print(f"  Emission Factors: {len(first_result['emission_factor'])} years of data")
            for ef in first_result['emission_factor'][:3]:  # Show first 3
                print(f"    - {ef['year']}: {ef['value']:.6f} kg CO2e/kWh")
        
        if 'emission_factor_coal' in first_result:
            print(f"  Coal Emission Factor: {first_result['emission_factor_coal']}")
    else:
        print(f"No results found for {plant_name}")

def test_natural_language_queries(tool):
    """Test natural language query processing."""
    print("\n" + "=" * 50)
    print("Testing Natural Language Queries")
    print("=" * 50)
    
    test_queries = [
        "What's the capacity of A B Brown plant?",
        "Show me coal plants with emission factors",
        "List plants with unit information",
        "Find plants with capacity data"
    ]
    
    for query in test_queries:
        print(f"\nQuery: '{query}'")
        results = tool.query(query)
        print(f"Results: {len(results)} matches")
        
        if results:
            # Show summary of first result
            first = results[0]
            plant_name = first.get('plant_id', 'Unknown')
            unit_num = first.get('unit_number', 'Unknown')
            capacity = first.get('capacity', 'Unknown')
            print(f"  First match: {plant_name} Unit {unit_num} ({capacity} MW)")

def test_json_schema_compliance(tool):
    """Test that output matches unit_level.json schema."""
    print("\n" + "=" * 50)
    print("Testing JSON Schema Compliance")
    print("=" * 50)
    
    # Get a sample result
    results = tool.query("Show me one coal plant")
    
    if results:
        sample_result = results[0]
        
        # Check required fields from unit_level.json
        required_fields = [
            'sk', 'capacity', 'capacity_unit', 'commencement_date', 
            'remaining_useful_life', 'unit_lifetime', 'unit_number', 'plant_id'
        ]
        
        print("Checking required fields:")
        for field in required_fields:
            if field in sample_result:
                print(f"  ✓ {field}: {type(sample_result[field]).__name__}")
            else:
                print(f"  ✗ {field}: MISSING")
        
        # Check array fields
        array_fields = ['fuel_type', 'emission_factor']
        print("\nChecking array fields:")
        for field in array_fields:
            if field in sample_result:
                if isinstance(sample_result[field], list):
                    print(f"  ✓ {field}: list with {len(sample_result[field])} items")
                else:
                    print(f"  ✗ {field}: not a list")
            else:
                print(f"  - {field}: not present (optional)")
        
        # Show sample JSON output
        print("\nSample JSON Output:")
        print(json.dumps(sample_result, indent=2)[:500] + "...")
    else:
        print("No results to test schema compliance")

def main():
    """Main test function."""
    print("Excel Power Plant Tool - Test Suite")
    print("=" * 50)
    
    # Test data loading
    tool = test_data_loading()
    
    if tool is None:
        print("Failed to load data. Exiting tests.")
        return
    
    # Test specific plant query
    test_specific_plant_query(tool)
    
    # Test natural language queries
    test_natural_language_queries(tool)
    
    # Test JSON schema compliance
    test_json_schema_compliance(tool)
    
    print("\n" + "=" * 50)
    print("Test Suite Complete")
    print("=" * 50)

if __name__ == "__main__":
    main()
