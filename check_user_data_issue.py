#!/usr/bin/env python3
"""
Check the specific data issue the user mentioned
"""

import sys
sys.path.append('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH-feat-agi-tech-int-007')
sys.path.append('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH-feat-agi-tech-int-007/backend/src')

from excel_power_plant_tool import ExcelPowerPlantTool
from agent.unit_extraction_stages import combine_unit_data
import pandas as pd

def check_user_reported_issue():
    """Check the specific issue the user reported"""
    
    print("🔍 CHECKING USER REPORTED ISSUE")
    print("=" * 60)
    print("User said: 'gcv_coal: 6690.0, (This is Lignite so the value should be 3350 right)'")
    print("User also mentioned: plf, auxiliary_power_consumed, gross_power_generation are empty")
    print()
    
    try:
        # Get the actual data that the user is seeing
        excel_tool = ExcelPowerPlantTool('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH-feat-agi-tech-int-007/USA Details.xlsx')
        
        # Test Cross Generating Station - all units
        plant_name = "Cross Generating Station"
        
        for unit_num in ['1', '2', '3', '4']:
            print(f"📊 UNIT {unit_num} ANALYSIS:")
            print("-" * 30)
            
            # Get Excel data
            excel_results = excel_tool.get_plant_data(plant_name, unit_num)
            
            if excel_results:
                excel_data = excel_results[0]
                
                # Create plant context
                plant_context = {
                    'excel_data': excel_data,
                    'plant_name': plant_name,
                    'country': 'United States',
                    'plant_uid': f'test-uuid-{unit_num}',
                    'plant_type': 'coal'
                }
                
                # Process unit data
                combined_data = combine_unit_data([], unit_num, plant_context)
                
                # Check the specific fields user mentioned
                gcv_coal = combined_data.get('gcv_coal')
                plf = combined_data.get('plf', [])
                auxiliary_power = combined_data.get('auxiliary_power_consumed', [])
                gross_power = combined_data.get('gross_power_generation', [])
                heat_rate = combined_data.get('heat_rate')
                fuel_type = combined_data.get('fuel_type', [])
                
                print(f"✅ gcv_coal: {gcv_coal}")
                print(f"✅ plf: {len(plf)} records")
                print(f"✅ auxiliary_power_consumed: {len(auxiliary_power)} records")
                print(f"✅ gross_power_generation: {len(gross_power)} records")
                print(f"✅ heat_rate: {heat_rate}")
                
                if fuel_type:
                    coal_type = fuel_type[0].get('type', 'Unknown')
                    print(f"✅ Coal type from Excel: {coal_type}")
                    
                    # Check if coal type matches GCV
                    if coal_type.lower() == 'lignite' and gcv_coal != 3350:
                        print(f"❌ MISMATCH: Coal type is Lignite but GCV is {gcv_coal} (should be 3350)")
                    elif coal_type.lower() == 'bituminous' and gcv_coal != 6690:
                        print(f"❌ MISMATCH: Coal type is Bituminous but GCV is {gcv_coal} (should be 6690)")
                    else:
                        print(f"✅ GCV matches coal type")
                
                # Check if arrays are empty (user's issue)
                if len(plf) == 0:
                    print("❌ PLF array is empty - this matches user's issue")
                if len(auxiliary_power) == 0:
                    print("❌ Auxiliary power array is empty - this matches user's issue")
                if len(gross_power) == 0:
                    print("❌ Gross power array is empty - this matches user's issue")
                
                print()
            
            else:
                print(f"❌ No data found for Unit {unit_num}")
                print()
        
        # Check if there are any plants that actually use Lignite
        print("🔍 SEARCHING FOR LIGNITE PLANTS IN EXCEL:")
        print("-" * 40)
        
        # Check coal sheets for Lignite
        excel_file = '/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH-feat-agi-tech-int-007/USA Details.xlsx'
        coal_sheets = ['Coal 2024', 'Coal 2023', 'Coal 2022']
        
        lignite_plants = []
        for sheet in coal_sheets:
            try:
                df = pd.read_excel(excel_file, sheet_name=sheet)
                lignite_data = df[df['Type'].str.contains('Lignite', na=False, case=False)]
                if not lignite_data.empty:
                    plants = lignite_data['Plant Name'].unique()
                    lignite_plants.extend(plants)
                    print(f"  {sheet}: Found {len(lignite_data)} Lignite records")
                    for plant in plants[:3]:  # Show first 3
                        print(f"    - {plant}")
            except Exception as e:
                print(f"  Error reading {sheet}: {e}")
        
        if lignite_plants:
            print(f"\n📊 Testing a Lignite plant to verify GCV calculation:")
            lignite_plant = lignite_plants[0]
            print(f"Testing: {lignite_plant}")
            
            # Test lignite plant
            lignite_results = excel_tool.get_plant_data(lignite_plant)
            if lignite_results:
                lignite_data = lignite_results[0]
                plant_context = {
                    'excel_data': lignite_data,
                    'plant_name': lignite_plant,
                    'country': 'United States',
                    'plant_uid': 'test-lignite',
                    'plant_type': 'coal'
                }
                
                combined_lignite = combine_unit_data([], "1", plant_context)
                lignite_gcv = combined_lignite.get('gcv_coal')
                lignite_fuel = combined_lignite.get('fuel_type', [])
                
                print(f"  Lignite plant GCV: {lignite_gcv}")
                if lignite_fuel:
                    print(f"  Lignite plant fuel type: {lignite_fuel[0].get('type')}")
                
                if lignite_gcv == 3350:
                    print("  ✅ Lignite GCV is correct (3350)")
                else:
                    print(f"  ❌ Lignite GCV is wrong (expected 3350, got {lignite_gcv})")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_user_reported_issue()
