#!/usr/bin/env python3
"""
Test the fixed USA Excel calculation engine with proper Case 1 methodology
"""

import sys
import os
sys.path.append('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH-feat-agi-tech-int-007')

from usa_excel_calculation_engine import USAExcelCalculationEngine
import json

def test_fixed_usa_case1_engine():
    """Test the fixed USA Excel calculation engine"""
    
    print("🧪 TESTING FIXED USA EXCEL CALCULATION ENGINE")
    print("=" * 60)
    print("📋 Using CASE 1: Plant-level data → Unit-level extrapolation")
    print()
    
    # Initialize the engine
    usa_details_path = '/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH-feat-agi-tech-int-007/USA Details.xlsx'
    calculations_path = '/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH-feat-agi-tech-int-007/current state calculations.xlsx'
    
    engine = USAExcelCalculationEngine(usa_details_path, calculations_path)
    
    # Test plant data extraction
    print("🔍 STEP 1: EXTRACT PLANT-LEVEL DATA")
    print("-" * 40)
    plant_data = engine.extract_plant_data_from_usa_excel("Antelope Valley")
    
    if plant_data:
        print(f"✅ Successfully extracted data for {len(plant_data)} years")
        for year, data in plant_data.items():
            print(f"  📅 {year}: {data['net_generation_mwh']:,.0f} MWh (Plant Total)")
    else:
        print("❌ No plant data extracted")
        return
    
    print()
    print("🔧 STEP 2: CALCULATE PLANT-LEVEL PLF (CASE 1)")
    print("-" * 40)
    
    plant_capacity_mw = 954  # 2 units × 477 MW each
    plant_plf_results = engine.calculate_plf_case1_plant_level(plant_data, plant_capacity_mw)
    
    if plant_plf_results:
        print(f"✅ Plant PLF calculated for {len(plant_plf_results)} years")
        for year, result in plant_plf_results.items():
            print(f"  📅 {year}: PLF = {result['plf']:.1%}")
            print(f"       Net: {result['net_generation_mwh']:,.0f} MWh")
            print(f"       Gross: {result['gross_generation_mwh']:,.0f} MWh")
    else:
        print("❌ No plant PLF results")
        return
    
    print()
    print("🔧 STEP 3: EXTRAPOLATE TO UNIT-LEVEL (CASE 1)")
    print("-" * 40)
    
    unit_capacity_mw = 477  # Correct capacity per unit
    unit_number = "2"
    
    unit_results = engine.calculate_unit_level_from_plant_plf(plant_plf_results, unit_capacity_mw, unit_number)
    
    if unit_results:
        print(f"✅ Unit-level data extrapolated for {len(unit_results)} years")
        for year, result in unit_results.items():
            print(f"  📅 {year}: Unit PLF = {result['plf']:.1%}")
            print(f"       Unit Net: {result['net_generation_mwh']:,.0f} MWh")
            print(f"       Unit Gross: {result['gross_generation_mwh']:,.0f} MWh")
    else:
        print("❌ No unit-level results")
        return
    
    print()
    print("🔧 STEP 4: COMPLETE UNIT CALCULATION")
    print("-" * 40)
    
    # Test complete unit calculation
    unit_data = {
        'capacity': 477,
        'unit_number': '2',
        'technology': 'subcritical'
    }
    
    complete_results = engine.calculate_unit_parameters_usa("Antelope Valley", unit_data)
    
    if complete_results:
        print("✅ Complete unit calculation successful")
        
        # Show key results
        plf_data = complete_results.get('plf', [])
        aux_power_data = complete_results.get('auxiliary_power_consumed', [])
        gross_gen_data = complete_results.get('gross_power_generation', [])
        
        print(f"📊 PLF Array Length: {len(plf_data)}")
        print(f"📊 Auxiliary Power Array Length: {len(aux_power_data)}")
        print(f"📊 Gross Generation Array Length: {len(gross_gen_data)}")
        
        if plf_data:
            plf_values = [f"{p['value']:.1%}" for p in plf_data[:3]]
            print(f"📊 PLF Values: {plf_values}...")
        if aux_power_data:
            aux_values = [f"{a['value']:.1%}" for a in aux_power_data[:3]]
            print(f"📊 Aux Power Values: {aux_values}...")
        if gross_gen_data:
            gross_values = [f"{g['value']:,.1f} GWh" for g in gross_gen_data[:3]]
            print(f"📊 Gross Gen Values: {gross_values}...")
        
        # Save test results
        output_file = 'antelope_valley_unit_2_case1_fixed.json'
        with open(output_file, 'w') as f:
            json.dump(complete_results, f, indent=2)
        print(f"💾 Results saved to: {output_file}")
        
    else:
        print("❌ Complete unit calculation failed")
    
    print()
    print("🎯 SUMMARY")
    print("-" * 20)
    print("✅ Fixed USA Excel calculation engine using proper Case 1 methodology")
    print("✅ Plant-level data aggregation working correctly")
    print("✅ Plant PLF calculation using correct 954 MW capacity")
    print("✅ Unit-level extrapolation using 477 MW per unit")
    print("✅ Arrays should now be populated with calculated values")

if __name__ == "__main__":
    test_fixed_usa_case1_engine()
