#!/usr/bin/env python3
"""
Debug script to trace the exact data flow and see where calculated values are lost
"""

import sys
import os
sys.path.append('backend/src')

from agent.power_plant_calculation_engine import PowerPlantCalculationEngine, create_calculation_engine
from agent.fallback_calculations import FallbackCalculationEngine
import json

def debug_data_flow():
    """Debug the exact data flow to see where calculated values are lost"""
    
    print("🔍 DEBUGGING DATA FLOW FOR CALCULATED VALUES")
    print("=" * 70)
    
    # Step 1: Create calculation engine
    calc_engine = create_calculation_engine('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH-feat-agi-tech-int-007/current state calculations.xlsx')
    fallback_engine = FallbackCalculationEngine()
    
    # Step 2: Simulate initial combined_data (before calculations)
    combined_data = {
        'capacity': 708.0,
        'technology': 'subcritical',
        'coal_type': 'lignite',
        'unit_number': 2,
        'plant_name': 'Antelope Valley Station',
        'plf': [],  # Empty initially
        'auxiliary_power_consumed': [],  # Empty initially
        'gross_power_generation': []  # Empty initially
    }
    
    unit_data = {
        'capacity': 708.0,
        'technology': 'subcritical',
        'coal_type': 'lignite',
        'unit_number': 2,
        'plant_name': 'Antelope Valley Station'
    }
    
    plant_context = {
        'plant_name': 'Antelope Valley Station',
        'country': 'USA',
        'plant_capacity_mw': 1416.0,
        'technology': 'subcritical',
        'capacity': 708.0
    }
    
    print("📊 STEP 1: Initial combined_data")
    print(f"PLF: {combined_data.get('plf', 'NOT_FOUND')}")
    print(f"Auxiliary Power: {combined_data.get('auxiliary_power_consumed', 'NOT_FOUND')}")
    print(f"Gross Power: {combined_data.get('gross_power_generation', 'NOT_FOUND')}")
    print()
    
    # Step 3: Apply PowerPlantCalculationEngine (simulate unit_extraction_stages.py logic)
    print("📊 STEP 2: Apply PowerPlantCalculationEngine")
    calc_results = calc_engine.calculate_unit_parameters(unit_data, plant_context)
    
    # Apply PLF results
    if 'time_series_plf' in calc_results:
        plf_time_series = []
        for year, value in calc_results['time_series_plf'].items():
            plf_time_series.append({"value": round(value * 100, 2), "year": str(year)})  # Convert to percentage
        combined_data['plf'] = plf_time_series
        print(f"✅ Applied calculated PLF time series: {len(plf_time_series)} years")
    
    # Apply auxiliary power results
    if 'auxiliary_power_percent' in calc_results:
        if 'time_series_auxiliary_power' in calc_results:
            aux_time_series = []
            for year, value in calc_results['time_series_auxiliary_power'].items():
                aux_time_series.append({"value": round(value, 3), "year": str(year)})  # Keep as decimal (e.g., 0.08 for 8%)
            combined_data['auxiliary_power_consumed'] = aux_time_series
            print(f"✅ Applied calculated auxiliary power time series: {len(aux_time_series)} years")
    
    # Apply gross power generation results
    if 'time_series_plf' in calc_results and combined_data.get('capacity'):
        capacity_mw = float(combined_data['capacity'])
        gross_power_time_series = []
        for year, plf_value in calc_results['time_series_plf'].items():
            gross_power_gwh = capacity_mw * plf_value * 8760 / 1000
            gross_power_time_series.append({"value": round(gross_power_gwh, 2), "year": str(year)})
        combined_data['gross_power_generation'] = gross_power_time_series
        print(f"✅ Calculated gross power generation time series: {len(gross_power_time_series)} years")
    
    print()
    print("📊 STEP 3: After PowerPlantCalculationEngine")
    print(f"PLF: {combined_data.get('plf', 'NOT_FOUND')}")
    print(f"Auxiliary Power: {combined_data.get('auxiliary_power_consumed', 'NOT_FOUND')}")
    print(f"Gross Power: {combined_data.get('gross_power_generation', 'NOT_FOUND')}")
    print()
    
    # Step 4: Apply fallback engine (simulate the problematic step)
    print("📊 STEP 4: Apply FallbackCalculationEngine")
    enhanced_data = fallback_engine.enhance_unit_data(
        extracted_data=combined_data,
        unit_context=plant_context,
        session_id="debug_test"
    )
    
    print()
    print("📊 STEP 5: After FallbackCalculationEngine")
    print(f"PLF in enhanced_data: {enhanced_data.get('plf', 'NOT_FOUND')}")
    print(f"Auxiliary Power in enhanced_data: {enhanced_data.get('auxiliary_power_consumed', 'NOT_FOUND')}")
    print(f"Gross Power in enhanced_data: {enhanced_data.get('gross_power_generation', 'NOT_FOUND')}")
    print()
    
    # Step 5: Update combined_data with enhanced_data (the problematic line)
    print("📊 STEP 6: combined_data.update(enhanced_data)")
    print("Before update:")
    print(f"  PLF: {len(combined_data.get('plf', []))} items")
    print(f"  Auxiliary Power: {len(combined_data.get('auxiliary_power_consumed', []))} items")
    print(f"  Gross Power: {len(combined_data.get('gross_power_generation', []))} items")
    
    combined_data.update(enhanced_data)
    
    print("After update:")
    print(f"  PLF: {len(combined_data.get('plf', []))} items")
    print(f"  Auxiliary Power: {len(combined_data.get('auxiliary_power_consumed', []))} items")
    print(f"  Gross Power: {len(combined_data.get('gross_power_generation', []))} items")
    print()
    
    # Step 6: Show final values
    print("📊 FINAL RESULT:")
    print(f"PLF: {combined_data.get('plf', 'NOT_FOUND')}")
    print(f"Auxiliary Power: {combined_data.get('auxiliary_power_consumed', 'NOT_FOUND')}")
    print(f"Gross Power: {combined_data.get('gross_power_generation', 'NOT_FOUND')}")

if __name__ == "__main__":
    debug_data_flow()
