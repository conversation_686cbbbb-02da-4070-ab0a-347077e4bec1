#!/usr/bin/env python3
"""
Test script to verify the fixes for unit processing issues
"""

import sys
sys.path.append('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH-feat-agi-tech-int-007')
sys.path.append('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH-feat-agi-tech-int-007/backend/src')

from excel_power_plant_tool import ExcelPowerPlantTool
from agent.unit_extraction_stages import combine_unit_data
from agent.power_plant_calculation_engine import create_calculation_engine
import json

def test_emission_factor_fix():
    """Test that emission factors now have correct years"""
    
    print("🔍 TESTING EMISSION FACTOR YEAR FIX")
    print("=" * 60)
    
    try:
        # Initialize Excel tool
        excel_tool = ExcelPowerPlantTool('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH-feat-agi-tech-int-007/USA Details.xlsx')
        
        # Test with Cross Generating Station Unit 1
        plant_name = "Cross Generating Station"
        unit_results = excel_tool.get_plant_data(plant_name, "1")
        
        if unit_results:
            unit_data = unit_results[0]
            emission_factor = unit_data.get('emission_factor', [])
            
            print(f"📊 Emission factor data: {emission_factor}")
            
            # Check if years are correct
            years_found = [ef.get('year') for ef in emission_factor if isinstance(ef, dict)]
            expected_years = [2020, 2021, 2022, 2023, 2024]
            
            print(f"📅 Years found: {years_found}")
            print(f"📅 Expected years: {expected_years}")
            
            # Check if we have the correct years
            correct_years = all(year in expected_years for year in years_found)
            if correct_years and len(set(years_found)) > 1:  # More than one unique year
                print("✅ EMISSION FACTOR YEARS FIXED!")
            else:
                print("❌ Emission factor years still incorrect")
                
        else:
            print("❌ No Excel data found for Unit 1")
            
    except Exception as e:
        print(f"❌ Emission factor test failed: {e}")

def test_gross_power_generation_calculation():
    """Test that gross power generation is now calculated"""
    
    print("\n🔍 TESTING GROSS POWER GENERATION CALCULATION")
    print("=" * 60)
    
    try:
        # Get Excel data for Unit 1
        excel_tool = ExcelPowerPlantTool('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH-feat-agi-tech-int-007/USA Details.xlsx')
        excel_results = excel_tool.get_plant_data("Cross Generating Station", "1")
        
        if excel_results:
            excel_data = excel_results[0]
            
            # Test combine_unit_data with Excel data
            plant_context = {
                'excel_data': excel_data,
                'plant_name': 'Cross Generating Station',
                'country': 'United States',
                'plant_uid': 'test-uuid-123',
                'plant_type': 'coal'
            }
            
            # Call combine_unit_data
            combined_data = combine_unit_data([], "1", plant_context)
            
            gross_power = combined_data.get('gross_power_generation', [])
            plf_data = combined_data.get('plf', [])
            capacity = combined_data.get('capacity')
            
            print(f"📊 Capacity: {capacity} MW")
            print(f"📊 PLF data: {plf_data}")
            print(f"📊 Gross power generation: {gross_power}")
            
            if gross_power and len(gross_power) > 0:
                print("✅ GROSS POWER GENERATION CALCULATION FIXED!")
                
                # Verify calculation
                if plf_data and capacity:
                    expected_gross = capacity * plf_data[0]['value'] * 8760 / 1000
                    actual_gross = gross_power[0]['value']
                    print(f"📊 Expected gross power (first year): {expected_gross:.2f} GWh")
                    print(f"📊 Actual gross power (first year): {actual_gross} GWh")
                    
                    if abs(expected_gross - actual_gross) < 1:  # Within 1 GWh tolerance
                        print("✅ Calculation is correct!")
                    else:
                        print("❌ Calculation seems incorrect")
            else:
                print("❌ Gross power generation still empty")
                
        else:
            print("❌ No Excel data found for Unit 1")
            
    except Exception as e:
        print(f"❌ Gross power generation test failed: {e}")

def test_all_units_processing():
    """Test that all units are processed correctly"""
    
    print("\n🔍 TESTING ALL UNITS PROCESSING")
    print("=" * 60)
    
    try:
        # Initialize Excel tool
        excel_tool = ExcelPowerPlantTool('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH-feat-agi-tech-int-007/USA Details.xlsx')
        
        # Test with Cross Generating Station - get all units
        plant_name = "Cross Generating Station"
        all_results = excel_tool.get_plant_data(plant_name)
        
        print(f"📊 Total units found: {len(all_results)}")
        
        # Process each unit individually
        for unit_num in ['1', '2', '3', '4']:
            unit_results = excel_tool.get_plant_data(plant_name, unit_num)
            if unit_results:
                unit_data = unit_results[0]
                
                # Test combine_unit_data for each unit
                plant_context = {
                    'excel_data': unit_data,
                    'plant_name': plant_name,
                    'country': 'United States',
                    'plant_uid': f'test-uuid-{unit_num}',
                    'plant_type': 'coal'
                }
                
                combined_data = combine_unit_data([], unit_num, plant_context)
                
                print(f"✅ Unit {unit_num}:")
                print(f"   - Capacity: {combined_data.get('capacity')} MW")
                print(f"   - PLF records: {len(combined_data.get('plf', []))}")
                print(f"   - Gross power records: {len(combined_data.get('gross_power_generation', []))}")
                print(f"   - Emission factor records: {len(combined_data.get('emission_factor', []))}")
                
            else:
                print(f"❌ Unit {unit_num}: No data found")
                
        print("✅ ALL UNITS PROCESSING TEST COMPLETED!")
        
    except Exception as e:
        print(f"❌ All units processing test failed: {e}")

if __name__ == "__main__":
    print("🧪 TESTING FIXED ISSUES")
    print("=" * 80)
    
    test_emission_factor_fix()
    test_gross_power_generation_calculation()
    test_all_units_processing()
    
    print("\n🎉 ALL TESTS COMPLETED!")
