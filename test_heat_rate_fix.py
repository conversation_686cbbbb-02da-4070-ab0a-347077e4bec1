#!/usr/bin/env python3
"""
Test the heat rate fix
"""

import sys
sys.path.append('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH-feat-agi-tech-int-007')
sys.path.append('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH-feat-agi-tech-int-007/backend/src')

from excel_power_plant_tool import ExcelPowerPlantTool
from agent.unit_extraction_stages import combine_unit_data

def test_heat_rate_fix():
    """Test that heat rate is now calculated correctly"""
    
    print("🔧 TESTING HEAT RATE FIX")
    print("=" * 60)
    
    try:
        # Get data for Cross Generating Station Unit 1
        excel_tool = ExcelPowerPlantTool('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH-feat-agi-tech-int-007/USA Details.xlsx')
        excel_results = excel_tool.get_plant_data("Cross Generating Station", "1")
        
        if excel_results:
            excel_data = excel_results[0]
            
            # Create plant context
            plant_context = {
                'excel_data': excel_data,
                'plant_name': 'Cross Generating Station',
                'country': 'United States',
                'plant_uid': 'test-uuid-1',
                'plant_type': 'coal'
            }
            
            # Process unit data
            combined_data = combine_unit_data([], "1", plant_context)
            
            # Extract key values
            capacity = combined_data.get('capacity')
            technology = combined_data.get('technology', 'subcritical')
            gcv_coal = combined_data.get('gcv_coal')
            heat_rate = combined_data.get('heat_rate')
            efficiency = combined_data.get('coal_unit_efficiency')
            fuel_type = combined_data.get('fuel_type', [])
            
            print(f"📊 RESULTS AFTER FIX:")
            print(f"  Capacity: {capacity} MW")
            print(f"  Technology: {technology}")
            print(f"  GCV Coal: {gcv_coal} kcal/kg")
            print(f"  Heat Rate: {heat_rate} kcal/kWh")
            print(f"  Efficiency: {efficiency}%")
            
            if fuel_type:
                coal_type = fuel_type[0].get('type', 'Unknown')
                print(f"  Coal Type: {coal_type}")
            
            # Verify heat rate calculation
            print(f"\n🔧 HEAT RATE VERIFICATION:")
            
            if efficiency:
                # Convert efficiency from percentage to decimal
                if efficiency > 1:  # It's a percentage
                    eff_decimal = efficiency / 100
                else:
                    eff_decimal = efficiency
                
                expected_heat_rate = 860.42 / eff_decimal
                print(f"  Efficiency (decimal): {eff_decimal}")
                print(f"  Expected Heat Rate: 860.42 / {eff_decimal} = {expected_heat_rate:.1f} kcal/kWh")
                print(f"  Actual Heat Rate: {heat_rate} kcal/kWh")
                
                if abs(expected_heat_rate - heat_rate) < 5:  # 5 kcal/kWh tolerance
                    print(f"  ✅ Heat rate calculation is now CORRECT!")
                    print(f"  Difference: {abs(expected_heat_rate - heat_rate):.1f} kcal/kWh")
                else:
                    print(f"  ❌ Heat rate calculation is still incorrect!")
                    print(f"  Difference: {abs(expected_heat_rate - heat_rate):.1f} kcal/kWh")
            
            # Check all the user's concerns
            print(f"\n🎯 USER'S CONCERNS STATUS:")
            
            # 1. Check arrays
            plf = combined_data.get('plf', [])
            auxiliary_power = combined_data.get('auxiliary_power_consumed', [])
            gross_power = combined_data.get('gross_power_generation', [])
            
            print(f"1. PLF array: {len(plf)} records {'✅' if len(plf) > 0 else '❌'}")
            print(f"2. Auxiliary power array: {len(auxiliary_power)} records {'✅' if len(auxiliary_power) > 0 else '❌'}")
            print(f"3. Gross power array: {len(gross_power)} records {'✅' if len(gross_power) > 0 else '❌'}")
            
            # 2. Check GCV
            if fuel_type:
                coal_type = fuel_type[0].get('type', 'Unknown')
                expected_gcv = {'Bituminous': 6690, 'Lignite': 3350, 'Sub-bituminous': 4900}
                expected = expected_gcv.get(coal_type, 6690)
                
                print(f"4. GCV Coal: {gcv_coal} kcal/kg for {coal_type} coal")
                print(f"   Expected: {expected} kcal/kg")
                print(f"   Status: {'✅ CORRECT' if gcv_coal == expected else '❌ INCORRECT'}")
            
            # 3. Check heat rate
            print(f"5. Heat Rate: {heat_rate} kcal/kWh")
            if efficiency:
                eff_decimal = efficiency / 100 if efficiency > 1 else efficiency
                expected_hr = 860.42 / eff_decimal
                print(f"   Expected: {expected_hr:.1f} kcal/kWh")
                print(f"   Status: {'✅ CORRECT' if abs(expected_hr - heat_rate) < 5 else '❌ INCORRECT'}")
            
            print(f"\n📋 SUMMARY:")
            all_good = (
                len(plf) > 0 and 
                len(auxiliary_power) > 0 and 
                len(gross_power) > 0 and
                (fuel_type and gcv_coal == expected_gcv.get(fuel_type[0].get('type', 'Bituminous'), 6690)) and
                (efficiency and abs(860.42 / (efficiency / 100 if efficiency > 1 else efficiency) - heat_rate) < 5)
            )
            
            if all_good:
                print("🎉 ALL USER ISSUES HAVE BEEN RESOLVED!")
            else:
                print("⚠️ Some issues may still remain")
        
        else:
            print("❌ No data found for Cross Generating Station Unit 1")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_heat_rate_fix()
