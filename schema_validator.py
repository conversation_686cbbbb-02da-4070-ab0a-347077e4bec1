#!/usr/bin/env python3
"""
Schema validation script for Excel Power Plant Tool output.
Validates that the tool output matches the unit_level.json schema requirements.
"""

import json
from datetime import datetime
from excel_power_plant_tool import ExcelPowerPlantTool

def load_schema():
    """Load the unit_level.json schema for validation."""
    try:
        with open('unit_level.json', 'r') as f:
            schema = json.load(f)
        return schema
    except Exception as e:
        print(f"Error loading schema: {e}")
        return None

def validate_timestamp(timestamp_str):
    """Validate timestamp format matches schema requirement."""
    try:
        # Expected format: yyyy-mm-ddThh:mm:ss.msZ
        dt = datetime.strptime(timestamp_str, '%Y-%m-%dT%H:%M:%S.%fZ')
        return True, f"Valid timestamp: {timestamp_str}"
    except ValueError as e:
        return False, f"Invalid timestamp format: {timestamp_str} - {e}"

def validate_field_types(result, schema):
    """Validate field types against schema."""
    validation_results = []
    
    # Check required string fields
    string_fields = ['sk', 'capacity_unit', 'commencement_date', 'remaining_useful_life', 'unit_number', 'plant_id']
    for field in string_fields:
        if field in result:
            if isinstance(result[field], str):
                validation_results.append((True, f"✓ {field}: string"))
            else:
                validation_results.append((False, f"✗ {field}: expected string, got {type(result[field])}"))
        else:
            validation_results.append((False, f"✗ {field}: missing required field"))
    
    # Check numeric fields
    numeric_fields = ['capacity', 'unit_lifetime']
    for field in numeric_fields:
        if field in result:
            if isinstance(result[field], (int, float)) or result[field] is None:
                validation_results.append((True, f"✓ {field}: numeric"))
            else:
                validation_results.append((False, f"✗ {field}: expected numeric, got {type(result[field])}"))
    
    # Check array fields
    array_fields = ['fuel_type', 'emission_factor']
    for field in array_fields:
        if field in result:
            if isinstance(result[field], list):
                validation_results.append((True, f"✓ {field}: array with {len(result[field])} items"))
                
                # Validate array contents
                if field == 'fuel_type' and result[field]:
                    for i, fuel in enumerate(result[field]):
                        if not isinstance(fuel, dict):
                            validation_results.append((False, f"✗ {field}[{i}]: expected dict"))
                        else:
                            required_fuel_fields = ['fuel', 'type']
                            for req_field in required_fuel_fields:
                                if req_field not in fuel:
                                    validation_results.append((False, f"✗ {field}[{i}].{req_field}: missing"))
                                elif not isinstance(fuel[req_field], str):
                                    validation_results.append((False, f"✗ {field}[{i}].{req_field}: expected string"))
                
                elif field == 'emission_factor' and result[field]:
                    for i, ef in enumerate(result[field]):
                        if not isinstance(ef, dict):
                            validation_results.append((False, f"✗ {field}[{i}]: expected dict"))
                        else:
                            if 'value' not in ef or not isinstance(ef['value'], (int, float)):
                                validation_results.append((False, f"✗ {field}[{i}].value: missing or not numeric"))
                            if 'year' not in ef or not isinstance(ef['year'], str):
                                validation_results.append((False, f"✗ {field}[{i}].year: missing or not string"))
            else:
                validation_results.append((False, f"✗ {field}: expected array, got {type(result[field])}"))
    
    return validation_results

def validate_calculated_fields(result):
    """Validate calculated fields like unit_lifetime."""
    validation_results = []
    
    if 'commencement_date' in result and 'remaining_useful_life' in result and 'unit_lifetime' in result:
        try:
            start_date = datetime.strptime(result['commencement_date'], '%Y-%m-%dT%H:%M:%S.%fZ')
            end_date = datetime.strptime(result['remaining_useful_life'], '%Y-%m-%dT%H:%M:%S.%fZ')
            calculated_lifetime = (end_date - start_date).days / 365.25
            
            if abs(calculated_lifetime - result['unit_lifetime']) < 0.1:  # Allow small rounding differences
                validation_results.append((True, f"✓ unit_lifetime calculation correct: {result['unit_lifetime']} years"))
            else:
                validation_results.append((False, f"✗ unit_lifetime calculation incorrect: expected ~{calculated_lifetime:.2f}, got {result['unit_lifetime']}"))
        except Exception as e:
            validation_results.append((False, f"✗ unit_lifetime calculation failed: {e}"))
    
    return validation_results

def validate_schema_compliance(tool):
    """Run comprehensive schema compliance validation."""
    print("=" * 60)
    print("COMPREHENSIVE SCHEMA COMPLIANCE VALIDATION")
    print("=" * 60)
    
    # Load schema
    schema = load_schema()
    if not schema:
        print("Cannot proceed without schema")
        return
    
    # Test with multiple queries
    test_queries = [
        "A B Brown plant",
        "Show me coal plants",
        "Find plants with capacity data"
    ]
    
    all_passed = True
    
    for query in test_queries:
        print(f"\nTesting query: '{query}'")
        print("-" * 40)
        
        results = tool.query(query)
        if not results:
            print("No results returned")
            continue
        
        # Test first result
        result = results[0]
        query_passed = True
        
        # Validate timestamps
        timestamp_fields = ['commencement_date', 'remaining_useful_life']
        for field in timestamp_fields:
            if field in result:
                is_valid, message = validate_timestamp(result[field])
                print(message)
                if not is_valid:
                    query_passed = False
        
        # Validate field types
        type_validations = validate_field_types(result, schema)
        for is_valid, message in type_validations:
            print(message)
            if not is_valid:
                query_passed = False
        
        # Validate calculated fields
        calc_validations = validate_calculated_fields(result)
        for is_valid, message in calc_validations:
            print(message)
            if not is_valid:
                query_passed = False
        
        # Check for schema-specific requirements
        if 'sk' in result:
            sk_parts = result['sk'].split('#')
            if len(sk_parts) == 5:
                print("✓ SK format correct: 5 parts separated by #")
            else:
                print(f"✗ SK format incorrect: expected 5 parts, got {len(sk_parts)}")
                query_passed = False
        
        if query_passed:
            print("✓ Query validation PASSED")
        else:
            print("✗ Query validation FAILED")
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 ALL SCHEMA COMPLIANCE TESTS PASSED!")
    else:
        print("❌ SOME SCHEMA COMPLIANCE TESTS FAILED")
    print("=" * 60)
    
    return all_passed

def test_edge_cases(tool):
    """Test edge cases and error handling."""
    print("\n" + "=" * 60)
    print("EDGE CASE TESTING")
    print("=" * 60)
    
    edge_cases = [
        "NonexistentPlant12345",
        "",
        "Show me plants with missing data",
        "What is the capacity of Unit 999 at Unknown Plant?"
    ]
    
    for case in edge_cases:
        print(f"\nTesting edge case: '{case}'")
        try:
            results = tool.query(case)
            print(f"Results: {len(results)} items returned")
            if results:
                # Check if results are still valid
                result = results[0]
                if 'sk' in result and 'plant_id' in result:
                    print("✓ Valid result structure maintained")
                else:
                    print("✗ Invalid result structure")
        except Exception as e:
            print(f"✗ Exception occurred: {e}")

def main():
    """Main validation function."""
    print("Excel Power Plant Tool - Schema Validation Suite")
    
    # Initialize tool
    try:
        tool = ExcelPowerPlantTool()
        print("✓ Tool initialized successfully")
    except Exception as e:
        print(f"✗ Tool initialization failed: {e}")
        return
    
    # Run schema compliance validation
    schema_passed = validate_schema_compliance(tool)
    
    # Run edge case testing
    test_edge_cases(tool)
    
    # Final summary
    print("\n" + "=" * 60)
    print("VALIDATION SUMMARY")
    print("=" * 60)
    if schema_passed:
        print("✅ Schema compliance: PASSED")
        print("✅ Tool is ready for production use")
    else:
        print("❌ Schema compliance: FAILED")
        print("❌ Tool needs fixes before production use")

if __name__ == "__main__":
    main()
