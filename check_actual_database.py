#!/usr/bin/env python3
"""
Check what's actually in the database to understand the search issue
"""

import sys
import os
from dotenv import load_dotenv

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend', 'src'))

# Load environment variables
load_dotenv()

def check_database_contents():
    """Check what's actually in the database"""
    
    print("🔍 CHECKING ACTUAL DATABASE CONTENTS")
    print("=" * 50)
    
    try:
        from agent.database_manager import get_database_manager, PowerPlantRegistry
        
        db_manager = get_database_manager()
        session = db_manager.get_session()
        
        try:
            # Get ALL records
            all_plants = session.query(PowerPlantRegistry).all()
            
            print(f"📊 TOTAL RECORDS: {len(all_plants)}")
            
            if len(all_plants) == 0:
                print("❌ DATABASE IS COMPLETELY EMPTY!")
                print("   This explains why no plants can be found")
                return False
            
            print(f"\n🏭 FIRST 10 PLANTS IN DATABASE:")
            print("-" * 50)
            
            for i, plant in enumerate(all_plants[:10], 1):
                print(f"{i:2d}. {plant.plant_name}")
                print(f"     Org: {plant.org_name}")
                print(f"     Country: {plant.country}")
                print(f"     Org ID: {plant.org_id}")
                print(f"     Created: {plant.created_at}")
                print()
            
            # Check for Antelope Valley specifically
            print(f"🔍 SEARCHING FOR 'ANTELOPE' IN DATABASE:")
            print("-" * 50)
            
            antelope_plants = session.query(PowerPlantRegistry).filter(
                PowerPlantRegistry.plant_name.ilike('%antelope%')
            ).all()
            
            if antelope_plants:
                print(f"✅ Found {len(antelope_plants)} plants with 'antelope':")
                for plant in antelope_plants:
                    print(f"   - {plant.plant_name}")
            else:
                print("❌ No plants found with 'antelope' in name")
            
            # Check for any plants with 'valley'
            valley_plants = session.query(PowerPlantRegistry).filter(
                PowerPlantRegistry.plant_name.ilike('%valley%')
            ).all()
            
            if valley_plants:
                print(f"\n✅ Found {len(valley_plants)} plants with 'valley':")
                for plant in valley_plants:
                    print(f"   - {plant.plant_name}")
            else:
                print("\n❌ No plants found with 'valley' in name")
            
            return True
            
        finally:
            session.close()
        
    except Exception as e:
        print(f"❌ Database check failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_specific_search():
    """Test the specific search function with debug output"""
    
    print("\n🔍 TESTING SPECIFIC SEARCH FUNCTION")
    print("=" * 50)
    
    try:
        from agent.database_manager import get_database_manager
        
        db_manager = get_database_manager()
        
        # Test the exact search that's failing
        plant_name = "Antelope Valley Station"
        
        print(f"🧪 Testing search for: '{plant_name}'")
        
        # Call the actual function with debug
        result = db_manager.check_plant_exists(plant_name)
        
        if result:
            print(f"✅ FOUND: {result['plant_name']}")
            print(f"   Organization: {result['org_name']}")
            print(f"   UID: {result['org_id']}")
        else:
            print(f"❌ NOT FOUND")
        
        return result is not None
        
    except Exception as e:
        print(f"❌ Search test failed: {e}")
        return False

def main():
    """Main function"""
    
    print("🧪 DATABASE REALITY CHECK")
    print("=" * 60)
    
    # Check what's actually in the database
    db_has_data = check_database_contents()
    
    if db_has_data:
        # Test the search function
        search_works = test_specific_search()
        
        print("\n" + "=" * 60)
        print("🏁 DIAGNOSIS")
        print("=" * 60)
        
        if search_works:
            print("✅ Database has data and search works")
            print("🎯 The original test might have been wrong")
        else:
            print("❌ Database has data but search is broken")
            print("🔧 Search logic needs more fixes")
    else:
        print("\n" + "=" * 60)
        print("🏁 DIAGNOSIS")
        print("=" * 60)
        print("❌ Database is empty - that's why nothing can be found")
        print("🔧 Need to populate database first")
    
    return True

if __name__ == "__main__":
    main()
