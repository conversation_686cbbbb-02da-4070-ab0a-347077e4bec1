#!/usr/bin/env python3
"""
Test data extraction for power plant calculations
Generate search queries for all required variables
"""

import json

def generate_search_queries():
    """Generate comprehensive search queries for all required calculation variables"""
    
    print("🔍 COMPREHENSIVE SEARCH QUERIES FOR POWER PLANT CALCULATIONS")
    print("=" * 80)
    
    # Primary variables that MUST be extracted from web search
    search_queries = {
        "generation_data": [
            "annual generation MWh",
            "electricity generation per year",
            "power generation statistics",
            "unit wise generation",
            "plant level generation",
            "gross generation MWh",
            "net generation MWh",
            "capacity factor",
            "load factor",
            "PLF plant load factor",
            "generation data by year",
            "electricity output annual",
            "power output statistics"
        ],
        
        "fuel_consumption": [
            "coal consumption tons per year",
            "fuel consumption annual",
            "coal usage statistics",
            "fuel consumption data",
            "coal burned annually",
            "fuel input tons",
            "coal consumption per unit",
            "annual fuel usage",
            "coal consumption by year",
            "fuel consumption statistics"
        ],
        
        "emissions_data": [
            "CO2 emissions annual",
            "carbon emissions per year",
            "emission factor kg CO2/kWh",
            "greenhouse gas emissions",
            "carbon dioxide emissions",
            "emissions statistics",
            "emission factor data",
            "CO2 emissions by year",
            "carbon footprint",
            "environmental emissions"
        ],
        
        "efficiency_data": [
            "plant efficiency percentage",
            "thermal efficiency",
            "unit efficiency",
            "heat rate kcal/kWh",
            "energy efficiency",
            "conversion efficiency",
            "plant performance efficiency",
            "boiler efficiency",
            "turbine efficiency",
            "overall plant efficiency"
        ],
        
        "auxiliary_power": [
            "auxiliary power consumption",
            "station use percentage",
            "auxiliary load",
            "plant auxiliary consumption",
            "station service power",
            "auxiliary power percentage",
            "internal consumption",
            "plant self consumption"
        ],
        
        "coal_specifications": [
            "coal type bituminous lignite",
            "coal grade specifications",
            "coal calorific value",
            "GCV gross calorific value",
            "coal quality parameters",
            "coal heating value",
            "coal specifications",
            "fuel specifications"
        ]
    }
    
    print("📋 SEARCH QUERY CATEGORIES:")
    print("-" * 40)
    
    for category, queries in search_queries.items():
        print(f"\n🎯 {category.upper().replace('_', ' ')}:")
        for i, query in enumerate(queries, 1):
            print(f"  {i:2d}. \"{query}\"")
    
    return search_queries

def test_current_unit_data():
    """Test what data is currently available in unit JSON"""
    
    print("\n" + "=" * 80)
    print("🔍 TESTING CURRENT UNIT DATA AVAILABILITY")
    print("=" * 80)
    
    # Sample unit data from Antelope Valley Station Unit 2
    current_unit_data = {
        "capacity": 477.0,
        "technology": "Sub-Critical",
        "coal_unit_efficiency": 0.38,
        "heat_rate": 2200.0,
        "fuel_type": "Lignite",
        "gcv_coal": 6690.0,  # WRONG - should be 3350 for Lignite
        "plf": [],
        "auxiliary_power_consumed": [],
        "gross_power_generation": [],
        "emission_factor": []
    }
    
    print("📊 CURRENT AVAILABLE DATA:")
    print("-" * 30)
    for key, value in current_unit_data.items():
        status = "✅" if value and value != [] else "❌"
        print(f"  {status} {key}: {value}")
    
    print("\n📋 MISSING CRITICAL DATA FOR CALCULATIONS:")
    print("-" * 40)
    
    missing_data = [
        "unit_generation_mwh - Annual generation at unit level",
        "plant_generation_mwh - Annual generation at plant level", 
        "fuel_consumed_tons - Annual fuel consumption",
        "annual_emission_mt - Annual emissions in million tons",
        "net_generation_mwh - Net generation data",
        "coal_emission_factor - Emission factor for coal type",
        "plant_efficiency - Overall plant efficiency (if different from unit)",
        "paf_plant - Plant availability factor",
        "paf_unit - Unit availability factor"
    ]
    
    for i, item in enumerate(missing_data, 1):
        print(f"  ❌ {i:2d}. {item}")
    
    print("\n🔧 DATA QUALITY ISSUES:")
    print("-" * 25)
    print("  ❌ 1. GCV for Lignite coal: 6690 (should be 3350)")
    print("  ❌ 2. Heat rate: 2200 (seems like default, not calculated)")
    print("  ❌ 3. Empty time series arrays for PLF, auxiliary power, gross generation")
    print("  ❌ 4. Missing emission factor time series data")

def test_calculation_scenarios():
    """Test different calculation scenarios based on available data"""
    
    print("\n" + "=" * 80)
    print("🧪 CALCULATION SCENARIOS TESTING")
    print("=" * 80)
    
    scenarios = [
        {
            "name": "Scenario 1: Only Capacity + Technology Available",
            "available_data": ["capacity", "technology"],
            "possible_calculations": [
                "Auxiliary power percentage (from technology matrix)",
                "Default efficiency (from technology lookup)",
                "Heat rate (from default efficiency)"
            ],
            "missing_for_plf": ["generation_mwh OR fuel_consumed_tons OR annual_emission_mt"],
            "status": "❌ Cannot calculate PLF - missing generation data"
        },
        {
            "name": "Scenario 2: Capacity + Technology + Unit Generation",
            "available_data": ["capacity", "technology", "unit_generation_mwh"],
            "possible_calculations": [
                "PLF Case 2: Direct unit PLF calculation",
                "Auxiliary power percentage",
                "Gross power generation (from PLF)",
                "Heat rate (if efficiency available)"
            ],
            "missing_for_plf": [],
            "status": "✅ Can calculate PLF and derived metrics"
        },
        {
            "name": "Scenario 3: Capacity + Technology + Plant Generation",
            "available_data": ["capacity", "technology", "plant_generation_mwh", "plant_capacity_mw"],
            "possible_calculations": [
                "PLF Case 1: Plant to unit extrapolation",
                "Unit generation (from plant PLF)",
                "Auxiliary power percentage",
                "Gross power generation"
            ],
            "missing_for_plf": [],
            "status": "✅ Can calculate PLF and derived metrics"
        },
        {
            "name": "Scenario 4: Capacity + Technology + Fuel Consumption",
            "available_data": ["capacity", "technology", "fuel_consumed_tons", "gcv_coal", "efficiency"],
            "possible_calculations": [
                "PLF Case 3: From fuel consumption",
                "Generation (from fuel and efficiency)",
                "Heat rate (from efficiency)",
                "Emissions (from fuel consumption)"
            ],
            "missing_for_plf": [],
            "status": "✅ Can calculate PLF and derived metrics"
        },
        {
            "name": "Scenario 5: Capacity + Technology + Emissions",
            "available_data": ["capacity", "technology", "annual_emission_mt", "coal_emission_factor", "gcv_coal", "efficiency"],
            "possible_calculations": [
                "PLF Case 4: From emissions",
                "Fuel consumption (from emissions)",
                "Generation (from fuel and efficiency)",
                "Heat rate (from efficiency)"
            ],
            "missing_for_plf": [],
            "status": "✅ Can calculate PLF and derived metrics"
        }
    ]
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n🎯 {scenario['name']}")
        print(f"   Available: {', '.join(scenario['available_data'])}")
        print(f"   Status: {scenario['status']}")
        print("   Possible calculations:")
        for calc in scenario['possible_calculations']:
            print(f"     • {calc}")
        if scenario['missing_for_plf']:
            print(f"   Missing for PLF: {', '.join(scenario['missing_for_plf'])}")

if __name__ == "__main__":
    # Generate search queries
    search_queries = generate_search_queries()
    
    # Test current data
    test_current_unit_data()
    
    # Test calculation scenarios
    test_calculation_scenarios()
    
    print("\n" + "=" * 80)
    print("🎯 CONCLUSION AND NEXT STEPS")
    print("=" * 80)
    print()
    print("1. 🔍 UPDATE WEB SEARCH SCHEMA:")
    print("   - Add generation_mwh extraction patterns")
    print("   - Add fuel_consumed_tons extraction patterns") 
    print("   - Add annual_emission_mt extraction patterns")
    print("   - Add time series data extraction (2020-2024)")
    print()
    print("2. 🔧 FIX DATA QUALITY ISSUES:")
    print("   - Correct GCV values for different coal types")
    print("   - Implement proper heat rate calculations")
    print("   - Fix emission factor time series")
    print()
    print("3. 🧪 TEST WITH REAL PLANTS:")
    print("   - Test USA plants (Excel data available)")
    print("   - Test non-USA plants (web search only)")
    print("   - Verify calculation accuracy")
    print()
    print("4. 📊 IMPLEMENT FALLBACK STRATEGIES:")
    print("   - When generation data not available")
    print("   - When only partial data available")
    print("   - When no time series data available")
