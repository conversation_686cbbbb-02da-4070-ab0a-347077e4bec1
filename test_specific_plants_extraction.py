#!/usr/bin/env python3
"""
Test complete unit-level extraction calculations for specific plants:
1. Bonanza Power Station
2. Cardinal Power Station
3. Louisa Generating Station
4. Biron Mill

This script validates all the implemented fixes work correctly for these plants.
"""

import sys
import os
import json
from datetime import datetime

# Add the project root to Python path
sys.path.append('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH-feat-agi-tech-int-007')

def test_plant_extraction(plant_name: str):
    """
    Test complete unit-level extraction for a specific plant
    
    Args:
        plant_name: Name of the plant to test
    """
    print(f"\n🏭 TESTING PLANT: {plant_name}")
    print("=" * 80)
    
    try:
        from excel_power_plant_tool import ExcelPowerPlantTool
        from usa_excel_calculation_engine import create_usa_excel_calculation_engine
        
        # Initialize tools
        excel_tool = ExcelPowerPlantTool('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH-feat-agi-tech-int-007/USA Details.xlsx', 'test')
        usa_engine = create_usa_excel_calculation_engine()
        
        # Get plant data
        unit_data = excel_tool.get_plant_data(plant_name)
        
        if not unit_data:
            print(f"❌ FAIL: No unit data found for {plant_name}")
            return
        
        print(f"✅ Found {len(unit_data)} units for {plant_name}")
        
        # Calculate plant capacity
        plant_capacity = usa_engine._calculate_plant_capacity_from_excel(plant_name)
        print(f"📊 Total Plant Capacity: {plant_capacity} MW")
        
        # Test each unit
        for i, unit in enumerate(unit_data):
            print(f"\n📋 UNIT {i+1} ANALYSIS:")
            print("-" * 40)
            
            # Basic unit info
            unit_id = unit.get('unit_id', 'Unknown')
            unit_capacity = unit.get('capacity', 0)
            commencement_date = unit.get('commencement_date', 'Unknown')
            remaining_life = unit.get('remaining_useful_life', 'Unknown')
            
            print(f"Unit ID: {unit_id}")
            print(f"Unit Capacity: {unit_capacity} MW")
            print(f"Commencement Date: {commencement_date}")
            print(f"Remaining Useful Life: {remaining_life}")
            
            # Test Fix 1: Auxiliary Power (Plant Capacity)
            print(f"\n🔧 FIX 1 - AUXILIARY POWER:")
            aux_power_unit = usa_engine.get_auxiliary_power_percentage(unit_capacity, 'subcritical')
            aux_power_plant = usa_engine.get_auxiliary_power_percentage(plant_capacity, 'subcritical')
            
            print(f"  Using Unit Capacity ({unit_capacity} MW): {aux_power_unit*100:.1f}%")
            print(f"  Using Plant Capacity ({plant_capacity} MW): {aux_power_plant*100:.1f}%")
            
            if plant_capacity != unit_capacity:
                print(f"  ✅ PASS: Different aux power for plant vs unit capacity")
            else:
                print(f"  ℹ️ INFO: Plant capacity equals unit capacity")
            
            # Test Fix 2: Weighted Emission Factor
            print(f"\n🏭 FIX 2 - WEIGHTED EMISSION FACTOR:")
            emission_factor = unit.get('emission_factor')
            fuel_types = unit.get('fuel_type', [])
            
            print(f"  Emission Factor: {emission_factor}")
            print(f"  Number of Fuel Types: {len(fuel_types)}")
            
            for j, fuel in enumerate(fuel_types):
                fuel_type = fuel.get('type', 'Unknown')
                print(f"    Fuel {j+1}: {fuel_type}")
            
            if emission_factor and emission_factor > 0:
                print(f"  ✅ PASS: Emission factor calculated")
            else:
                print(f"  ❌ FAIL: No emission factor")
            
            # Test Fix 3: Simplified Remaining Useful Life
            print(f"\n⏰ FIX 3 - REMAINING USEFUL LIFE:")
            if remaining_life and remaining_life != 'Unknown':
                try:
                    # Parse the remaining life date
                    remaining_date = datetime.fromisoformat(remaining_life.replace('Z', '+00:00'))
                    commencement_dt = datetime.fromisoformat(commencement_date.replace('Z', '+00:00'))
                    
                    years_diff = (remaining_date - commencement_dt).days / 365.25
                    print(f"  Calculated Lifetime: {years_diff:.1f} years")
                    
                    if 45 <= years_diff <= 55:
                        print(f"  ✅ PASS: Reasonable lifetime (around 50 years)")
                    else:
                        print(f"  ⚠️ WARNING: Unusual lifetime: {years_diff:.1f} years")
                        
                except Exception as e:
                    print(f"  ❌ ERROR: Could not parse dates: {e}")
            else:
                print(f"  ❌ FAIL: No remaining useful life calculated")
            
            # Test Fix 4: Fuel Percentage Time Range
            print(f"\n⛽ FIX 4 - FUEL PERCENTAGE TIME RANGE:")
            if fuel_types:
                fuel = fuel_types[0]
                years_percentage = fuel.get('years_percentage', {})
                
                if years_percentage:
                    years = sorted([int(y) for y in years_percentage.keys() if y.isdigit()])
                    min_year = min(years) if years else 0
                    max_year = max(years) if years else 0
                    
                    print(f"  Years Range: {min_year} to {max_year}")
                    print(f"  Total Years: {len(years)}")
                    
                    # Check if commencement year is included
                    try:
                        comm_year = int(commencement_date.split('-')[0])
                        if min_year <= comm_year <= max_year:
                            print(f"  ✅ PASS: Includes commencement year ({comm_year})")
                        else:
                            print(f"  ❌ FAIL: Missing commencement year ({comm_year})")
                    except:
                        print(f"  ⚠️ WARNING: Could not parse commencement year")
                    
                    # Check if it's not fixed 2050
                    if max_year != 2050:
                        print(f"  ✅ PASS: Not using fixed 2050 endpoint")
                    else:
                        print(f"  ⚠️ WARNING: May be using fixed 2050 endpoint")
                else:
                    print(f"  ❌ FAIL: No fuel percentage data")
            else:
                print(f"  ❌ FAIL: No fuel types")
            
            # Test Fix 5: Aggregated Net Generation
            print(f"\n📊 FIX 5 - AGGREGATED NET GENERATION:")
            heat_rate = unit.get('heat_rate')
            efficiency = unit.get('coal_unit_efficiency')
            
            print(f"  Heat Rate: {heat_rate}")
            print(f"  Efficiency: {efficiency}")
            
            if heat_rate and efficiency:
                print(f"  ✅ PASS: Heat rate and efficiency calculated")
            else:
                print(f"  ❌ FAIL: Missing heat rate or efficiency")
            
            # Summary for this unit
            print(f"\n📋 UNIT {i+1} SUMMARY:")
            checks = [
                ("Auxiliary Power", aux_power_plant is not None),
                ("Emission Factor", emission_factor is not None and emission_factor > 0),
                ("Remaining Life", remaining_life and remaining_life != 'Unknown'),
                ("Fuel Percentage", fuel_types and len(fuel_types) > 0),
                ("Heat Rate/Efficiency", heat_rate and efficiency)
            ]
            
            passed = sum(1 for _, check in checks if check)
            total = len(checks)
            
            print(f"  Checks Passed: {passed}/{total}")
            
            if passed == total:
                print(f"  ✅ ALL CHECKS PASSED")
            elif passed >= total * 0.8:
                print(f"  ⚠️ MOSTLY PASSED ({passed}/{total})")
            else:
                print(f"  ❌ MULTIPLE FAILURES ({passed}/{total})")
        
        print(f"\n🎯 PLANT SUMMARY: {plant_name}")
        print(f"Units Processed: {len(unit_data)}")
        print(f"Plant Capacity: {plant_capacity} MW")
        
    except Exception as e:
        print(f"❌ ERROR testing {plant_name}: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Test all requested plants"""
    print("🚀 COMPLETE UNIT-LEVEL EXTRACTION TEST")
    print("=" * 80)
    print(f"Test started at: {datetime.now()}")
    
    # Test plants as requested
    test_plants = [
        "Bonanza Power Station",
        "Cardinal Power Station", 
        "Louisa Generating Station",
        "Biron Mill"
    ]
    
    for plant_name in test_plants:
        test_plant_extraction(plant_name)
    
    print(f"\n✅ ALL PLANT TESTS COMPLETED")
    print(f"Test finished at: {datetime.now()}")
    print("=" * 80)

if __name__ == "__main__":
    main()
