{"test_info": {"timestamp": "2025-08-05T10:31:18.528552", "test_type": "Plant and Unit Level Calculations", "plants_tested": ["Antelope Valley Station", "Dry Fork Station", "Laramie River Station"]}, "results": {"Antelope Valley Station": {"plant_level": {"plant_name": "Antelope Valley Station", "plant_level_fields": {"2024": {"auxiliary_power_consumed": 639200.6067415727, "emission_factor": 0.9346510516086853, "gross_power_generation": 5810914.606741573, "PAF": 1.0, "plf": 0}, "2023": {"auxiliary_power_consumed": 605614.1460674154, "emission_factor": 0.9327546183550467, "gross_power_generation": 5505583.146067415, "PAF": 1.0, "plf": 0}, "2022": {"auxiliary_power_consumed": 724047.561797753, "emission_factor": 0.9493155633094722, "gross_power_generation": 6582250.561797753, "PAF": 1.0, "plf": 0}, "2021": {"auxiliary_power_consumed": 644617.3033707868, "emission_factor": 0.8738121971006382, "gross_power_generation": 5860157.303370787, "PAF": 1.0, "plf": 0}, "2020": {"auxiliary_power_consumed": 698997.3483146066, "emission_factor": 0.8666404022969326, "gross_power_generation": 6354521.348314607, "PAF": 1.0, "plf": 0}}, "plant_capacity": 0.0, "calculation_details": {"2024": {"net_generation": 5171714.0, "aux_power_formula": "Gross - Net", "plf_formula": "Gross / (Plant_Capacity * 8760)", "paf_value": "Hardcoded to 1.0 as requested"}, "2023": {"net_generation": 4899969.0, "aux_power_formula": "Gross - Net", "plf_formula": "Gross / (Plant_Capacity * 8760)", "paf_value": "Hardcoded to 1.0 as requested"}, "2022": {"net_generation": 5858203.0, "aux_power_formula": "Gross - Net", "plf_formula": "Gross / (Plant_Capacity * 8760)", "paf_value": "Hardcoded to 1.0 as requested"}, "2021": {"net_generation": 5215540.0, "aux_power_formula": "Gross - Net", "plf_formula": "Gross / (Plant_Capacity * 8760)", "paf_value": "Hardcoded to 1.0 as requested"}, "2020": {"net_generation": 5655524.0, "aux_power_formula": "Gross - Net", "plf_formula": "Gross / (Plant_Capacity * 8760)", "paf_value": "Hardcoded to 1.0 as requested"}}, "errors": []}, "unit_level": {"plant_name": "Antelope Valley Station", "units": {"1": {"unit_id": "1", "capacity_mw": 2.3, "technology": "subcritical", "fuel_type": "coal", "calculations": {"auxiliary_power": {"percentage": 11.0, "decimal": 0.11, "method": "Capacity and technology based lookup"}, "gcv": {"coal_type": "bituminous", "gcv_kcal_per_kg": 6690, "source": "Hardcoded values from Assumptions sheet"}}, "errors": ["Unit 1 calculation error: calculate_unit_parameters_usa() missing 1 required positional argument: 'unit_data'"]}, "2": {"unit_id": "2", "capacity_mw": 477.0, "technology": "subcritical", "fuel_type": [{"fuel": "Coal", "type": "Lignite", "years_percentage": {"2024": 1.0, "2023": 1.0, "2022": 1.0, "2021": 0.06748198652488524, "2020": 0.004786647532571695, "1986": 0.0, "1987": 0.0, "1988": 0.0, "1989": 0.0, "1990": 0.0, "1991": 0.0, "1992": 0.0, "1993": 0.0, "1994": 0.0, "1995": 0.0, "1996": 0.0, "1997": 0.0, "1998": 0.0, "1999": 0.0, "2000": 0.0, "2001": 0.0, "2002": 0.0, "2003": 0.0, "2004": 0.0, "2005": 0.0, "2006": 0.0, "2007": 0.0, "2008": 0.0, "2009": 0.0, "2010": 0.0, "2011": 0.0, "2012": 0.0, "2013": 0.0, "2014": 0.0, "2015": 0.0, "2016": 0.0, "2017": 0.0, "2018": 0.0, "2019": 0.0}, "gcv_coal": 3350}, {"fuel": "Coal", "type": "Refined Coal", "years_percentage": {"2021": 0.9325180134751148, "2020": 0.9952133524674283, "1986": 0.0, "1987": 0.0, "1988": 0.0, "1989": 0.0, "1990": 0.0, "1991": 0.0, "1992": 0.0, "1993": 0.0, "1994": 0.0, "1995": 0.0, "1996": 0.0, "1997": 0.0, "1998": 0.0, "1999": 0.0, "2000": 0.0, "2001": 0.0, "2002": 0.0, "2003": 0.0, "2004": 0.0, "2005": 0.0, "2006": 0.0, "2007": 0.0, "2008": 0.0, "2009": 0.0, "2010": 0.0, "2011": 0.0, "2012": 0.0, "2013": 0.0, "2014": 0.0, "2015": 0.0, "2016": 0.0, "2017": 0.0, "2018": 0.0, "2019": 0.0, "2022": 0.0, "2023": 0.0, "2024": 0.0}, "gcv_coal": 6690}], "calculations": {"auxiliary_power": {"percentage": 10.0, "decimal": 0.1, "method": "Capacity and technology based lookup"}, "gcv": {"coal_type": "lignite", "gcv_kcal_per_kg": 3350, "source": "Hardcoded values from Assumptions sheet"}}, "errors": ["Unit 2 calculation error: calculate_unit_parameters_usa() missing 1 required positional argument: 'unit_data'"]}}, "errors": []}}, "Dry Fork Station": {"plant_level": {"plant_name": "Dry Fork Station", "plant_level_fields": {"2024": {"auxiliary_power_consumed": 296171.7777777775, "emission_factor": 1.1641139651553025, "gross_power_generation": 2961717.7777777775, "PAF": 1.0, "plf": 0.6989779547914472}, "2023": {"auxiliary_power_consumed": 316387.222222222, "emission_factor": 1.1457567311914112, "gross_power_generation": 3163872.222222222, "PAF": 1.0, "plf": 0.7466872609211487}, "2022": {"auxiliary_power_consumed": 298570.111111111, "emission_factor": 1.1086524428176816, "gross_power_generation": 2985701.111111111, "PAF": 1.0, "plf": 0.7046381231600192}, "2021": {"auxiliary_power_consumed": 326743.222222222, "emission_factor": 1.139707749307937, "gross_power_generation": 3267432.222222222, "PAF": 1.0, "plf": 0.7711278600698341}, "2020": {"auxiliary_power_consumed": 355973.0, "emission_factor": 1.0582671501997734, "gross_power_generation": 3559730.0, "PAF": 1.0, "plf": 0.8401113751211882}}, "plant_capacity": 483.7, "calculation_details": {"2024": {"net_generation": 2665546.0, "aux_power_formula": "Gross - Net", "plf_formula": "Gross / (Plant_Capacity * 8760)", "paf_value": "Hardcoded to 1.0 as requested"}, "2023": {"net_generation": 2847485.0, "aux_power_formula": "Gross - Net", "plf_formula": "Gross / (Plant_Capacity * 8760)", "paf_value": "Hardcoded to 1.0 as requested"}, "2022": {"net_generation": 2687131.0, "aux_power_formula": "Gross - Net", "plf_formula": "Gross / (Plant_Capacity * 8760)", "paf_value": "Hardcoded to 1.0 as requested"}, "2021": {"net_generation": 2940689.0, "aux_power_formula": "Gross - Net", "plf_formula": "Gross / (Plant_Capacity * 8760)", "paf_value": "Hardcoded to 1.0 as requested"}, "2020": {"net_generation": 3203757.0, "aux_power_formula": "Gross - Net", "plf_formula": "Gross / (Plant_Capacity * 8760)", "paf_value": "Hardcoded to 1.0 as requested"}}, "errors": []}, "unit_level": {"plant_name": "Dry Fork Station", "units": {"1": {"unit_id": "1", "capacity_mw": 483.7, "technology": "subcritical", "fuel_type": [{"fuel": "Coal", "type": "Sub Bituminous", "years_percentage": {"2024": 1.0, "2023": 1.0, "2022": 1.0, "2021": 1.0, "2020": 1.0, "2011": 0.0, "2012": 0.0, "2013": 0.0, "2014": 0.0, "2015": 0.0, "2016": 0.0, "2017": 0.0, "2018": 0.0, "2019": 0.0}, "gcv_coal": 4900}], "calculations": {"auxiliary_power": {"percentage": 10.0, "decimal": 0.1, "method": "Capacity and technology based lookup"}, "gcv": {"coal_type": "sub bituminous", "gcv_kcal_per_kg": 4900, "source": "Hardcoded values from Assumptions sheet"}}, "errors": ["Unit 1 calculation error: calculate_unit_parameters_usa() missing 1 required positional argument: 'unit_data'"]}}, "errors": []}}, "Laramie River Station": {"plant_level": {"plant_name": "Laramie River Station", "plant_level_fields": {"2024": {"auxiliary_power_consumed": 509310.36559139844, "emission_factor": 1.0659660987075295, "gross_power_generation": 7275862.365591398, "PAF": 1.0, "plf": 0.4458281780007818}, "2023": {"auxiliary_power_consumed": 711001.064516129, "emission_factor": 1.030193825053335, "gross_power_generation": 10157158.064516129, "PAF": 1.0, "plf": 0.6223794577237166}, "2022": {"auxiliary_power_consumed": 740038.344086023, "emission_factor": 1.0045667744676126, "gross_power_generation": 10571976.344086023, "PAF": 1.0, "plf": 0.647797431358933}, "2021": {"auxiliary_power_consumed": 623213.8387096785, "emission_factor": 0.7998981475977215, "gross_power_generation": 8903054.838709679, "PAF": 1.0, "plf": 0.5455343322812226}, "2020": {"auxiliary_power_consumed": 601526.408602152, "emission_factor": 0.7932489391452244, "gross_power_generation": 8593234.408602152, "PAF": 1.0, "plf": 0.5265500977091836}}, "plant_capacity": 1863.0, "calculation_details": {"2024": {"net_generation": 6766552.0, "aux_power_formula": "Gross - Net", "plf_formula": "Gross / (Plant_Capacity * 8760)", "paf_value": "Hardcoded to 1.0 as requested"}, "2023": {"net_generation": 9446157.0, "aux_power_formula": "Gross - Net", "plf_formula": "Gross / (Plant_Capacity * 8760)", "paf_value": "Hardcoded to 1.0 as requested"}, "2022": {"net_generation": 9831938.0, "aux_power_formula": "Gross - Net", "plf_formula": "Gross / (Plant_Capacity * 8760)", "paf_value": "Hardcoded to 1.0 as requested"}, "2021": {"net_generation": 8279841.0, "aux_power_formula": "Gross - Net", "plf_formula": "Gross / (Plant_Capacity * 8760)", "paf_value": "Hardcoded to 1.0 as requested"}, "2020": {"net_generation": 7991708.0, "aux_power_formula": "Gross - Net", "plf_formula": "Gross / (Plant_Capacity * 8760)", "paf_value": "Hardcoded to 1.0 as requested"}}, "errors": []}, "unit_level": {"plant_name": "Laramie River Station", "units": {"1": {"unit_id": "1", "capacity_mw": 621.0, "technology": "subcritical", "fuel_type": [{"fuel": "Coal", "type": "Refined Coal", "years_percentage": {"2021": 0.8536055221350265, "2020": 0.9595177901895314, "1981": 0.0, "1982": 0.0, "1983": 0.0, "1984": 0.0, "1985": 0.0, "1986": 0.0, "1987": 0.0, "1988": 0.0, "1989": 0.0, "1990": 0.0, "1991": 0.0, "1992": 0.0, "1993": 0.0, "1994": 0.0, "1995": 0.0, "1996": 0.0, "1997": 0.0, "1998": 0.0, "1999": 0.0, "2000": 0.0, "2001": 0.0, "2002": 0.0, "2003": 0.0, "2004": 0.0, "2005": 0.0, "2006": 0.0, "2007": 0.0, "2008": 0.0, "2009": 0.0, "2010": 0.0, "2011": 0.0, "2012": 0.0, "2013": 0.0, "2014": 0.0, "2015": 0.0, "2016": 0.0, "2017": 0.0, "2018": 0.0, "2019": 0.0, "2022": 0.0, "2023": 0.0, "2024": 0.0}, "gcv_coal": 6690}, {"fuel": "Coal", "type": "Sub Bituminous", "years_percentage": {"2024": 1.0, "2023": 1.0, "2022": 1.0, "2021": 0.1463944778649735, "2020": 0.04048220981046855, "1981": 0.0, "1982": 0.0, "1983": 0.0, "1984": 0.0, "1985": 0.0, "1986": 0.0, "1987": 0.0, "1988": 0.0, "1989": 0.0, "1990": 0.0, "1991": 0.0, "1992": 0.0, "1993": 0.0, "1994": 0.0, "1995": 0.0, "1996": 0.0, "1997": 0.0, "1998": 0.0, "1999": 0.0, "2000": 0.0, "2001": 0.0, "2002": 0.0, "2003": 0.0, "2004": 0.0, "2005": 0.0, "2006": 0.0, "2007": 0.0, "2008": 0.0, "2009": 0.0, "2010": 0.0, "2011": 0.0, "2012": 0.0, "2013": 0.0, "2014": 0.0, "2015": 0.0, "2016": 0.0, "2017": 0.0, "2018": 0.0, "2019": 0.0}, "gcv_coal": 4900}], "calculations": {"auxiliary_power": {"percentage": 9.0, "decimal": 0.09, "method": "Capacity and technology based lookup"}, "gcv": {"coal_type": "refined coal", "gcv_kcal_per_kg": 6690, "source": "Hardcoded values from Assumptions sheet"}}, "errors": ["Unit 1 calculation error: calculate_unit_parameters_usa() missing 1 required positional argument: 'unit_data'"]}, "2": {"unit_id": "2", "capacity_mw": 621.0, "technology": "subcritical", "fuel_type": [{"fuel": "Coal", "type": "Refined Coal", "years_percentage": {"2021": 0.8536055221350265, "2020": 0.9595177901895314, "1981": 0.0, "1982": 0.0, "1983": 0.0, "1984": 0.0, "1985": 0.0, "1986": 0.0, "1987": 0.0, "1988": 0.0, "1989": 0.0, "1990": 0.0, "1991": 0.0, "1992": 0.0, "1993": 0.0, "1994": 0.0, "1995": 0.0, "1996": 0.0, "1997": 0.0, "1998": 0.0, "1999": 0.0, "2000": 0.0, "2001": 0.0, "2002": 0.0, "2003": 0.0, "2004": 0.0, "2005": 0.0, "2006": 0.0, "2007": 0.0, "2008": 0.0, "2009": 0.0, "2010": 0.0, "2011": 0.0, "2012": 0.0, "2013": 0.0, "2014": 0.0, "2015": 0.0, "2016": 0.0, "2017": 0.0, "2018": 0.0, "2019": 0.0, "2022": 0.0, "2023": 0.0, "2024": 0.0}, "gcv_coal": 6690}, {"fuel": "Coal", "type": "Sub Bituminous", "years_percentage": {"2024": 1.0, "2023": 1.0, "2022": 1.0, "2021": 0.1463944778649735, "2020": 0.04048220981046855, "1981": 0.0, "1982": 0.0, "1983": 0.0, "1984": 0.0, "1985": 0.0, "1986": 0.0, "1987": 0.0, "1988": 0.0, "1989": 0.0, "1990": 0.0, "1991": 0.0, "1992": 0.0, "1993": 0.0, "1994": 0.0, "1995": 0.0, "1996": 0.0, "1997": 0.0, "1998": 0.0, "1999": 0.0, "2000": 0.0, "2001": 0.0, "2002": 0.0, "2003": 0.0, "2004": 0.0, "2005": 0.0, "2006": 0.0, "2007": 0.0, "2008": 0.0, "2009": 0.0, "2010": 0.0, "2011": 0.0, "2012": 0.0, "2013": 0.0, "2014": 0.0, "2015": 0.0, "2016": 0.0, "2017": 0.0, "2018": 0.0, "2019": 0.0}, "gcv_coal": 4900}], "calculations": {"auxiliary_power": {"percentage": 9.0, "decimal": 0.09, "method": "Capacity and technology based lookup"}, "gcv": {"coal_type": "refined coal", "gcv_kcal_per_kg": 6690, "source": "Hardcoded values from Assumptions sheet"}}, "errors": ["Unit 2 calculation error: calculate_unit_parameters_usa() missing 1 required positional argument: 'unit_data'"]}, "3": {"unit_id": "3", "capacity_mw": 621.0, "technology": "subcritical", "fuel_type": [{"fuel": "Coal", "type": "Refined Coal", "years_percentage": {"2021": 0.8536055221350265, "2020": 0.9595177901895314, "1982": 0.0, "1983": 0.0, "1984": 0.0, "1985": 0.0, "1986": 0.0, "1987": 0.0, "1988": 0.0, "1989": 0.0, "1990": 0.0, "1991": 0.0, "1992": 0.0, "1993": 0.0, "1994": 0.0, "1995": 0.0, "1996": 0.0, "1997": 0.0, "1998": 0.0, "1999": 0.0, "2000": 0.0, "2001": 0.0, "2002": 0.0, "2003": 0.0, "2004": 0.0, "2005": 0.0, "2006": 0.0, "2007": 0.0, "2008": 0.0, "2009": 0.0, "2010": 0.0, "2011": 0.0, "2012": 0.0, "2013": 0.0, "2014": 0.0, "2015": 0.0, "2016": 0.0, "2017": 0.0, "2018": 0.0, "2019": 0.0, "2022": 0.0, "2023": 0.0, "2024": 0.0}, "gcv_coal": 6690}, {"fuel": "Coal", "type": "Sub Bituminous", "years_percentage": {"2024": 1.0, "2023": 1.0, "2022": 1.0, "2021": 0.1463944778649735, "2020": 0.04048220981046855, "1982": 0.0, "1983": 0.0, "1984": 0.0, "1985": 0.0, "1986": 0.0, "1987": 0.0, "1988": 0.0, "1989": 0.0, "1990": 0.0, "1991": 0.0, "1992": 0.0, "1993": 0.0, "1994": 0.0, "1995": 0.0, "1996": 0.0, "1997": 0.0, "1998": 0.0, "1999": 0.0, "2000": 0.0, "2001": 0.0, "2002": 0.0, "2003": 0.0, "2004": 0.0, "2005": 0.0, "2006": 0.0, "2007": 0.0, "2008": 0.0, "2009": 0.0, "2010": 0.0, "2011": 0.0, "2012": 0.0, "2013": 0.0, "2014": 0.0, "2015": 0.0, "2016": 0.0, "2017": 0.0, "2018": 0.0, "2019": 0.0}, "gcv_coal": 4900}], "calculations": {"auxiliary_power": {"percentage": 9.0, "decimal": 0.09, "method": "Capacity and technology based lookup"}, "gcv": {"coal_type": "refined coal", "gcv_kcal_per_kg": 6690, "source": "Hardcoded values from Assumptions sheet"}}, "errors": ["Unit 3 calculation error: calculate_unit_parameters_usa() missing 1 required positional argument: 'unit_data'"]}}, "errors": []}}}}