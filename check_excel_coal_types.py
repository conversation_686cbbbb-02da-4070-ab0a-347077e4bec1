#!/usr/bin/env python3
"""
Check Excel sheet for actual coal types
"""

import pandas as pd

def check_excel_coal_types():
    """Check Excel sheet for Cross Generating Station coal types"""
    
    print("🔍 CHECKING EXCEL SHEET FOR COAL TYPES")
    print("=" * 60)
    
    try:
        excel_file = '/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH-feat-agi-tech-int-007/USA Details.xlsx'
        
        # Load coal sheets to see what coal types are available for Cross Generating Station
        coal_sheets = ['Coal 2024', 'Coal 2023', 'Coal 2022', 'Coal 2021', 'Coal 2020']
        
        for sheet in coal_sheets:
            print(f"\n📊 Checking {sheet}:")
            try:
                df = pd.read_excel(excel_file, sheet_name=sheet)
                print(f"  Columns: {list(df.columns)}")
                
                # Look for Cross Generating Station
                cross_data = df[df['Plant Name'].str.contains('Cross', na=False)]
                if not cross_data.empty:
                    print(f"  Found {len(cross_data)} records for Cross Generating Station")
                    
                    # Show coal types
                    if 'Type' in cross_data.columns:
                        coal_types = cross_data['Type'].unique()
                        print(f"  Coal types: {coal_types}")
                    
                    # Show sample data
                    print(f"  Sample data:")
                    for idx, row in cross_data.head(3).iterrows():
                        print(f"    Unit {row.get('Unit', 'N/A')}: Type={row.get('Type', 'N/A')}, EF={row.get('Emission Factor', 'N/A')}")
                else:
                    print(f"  No Cross Generating Station data found")
                    
            except Exception as e:
                print(f"  Error reading {sheet}: {e}")
        
        # Check USA Details sheet
        print(f"\n📊 Checking USA Details sheet:")
        usa_df = pd.read_excel(excel_file, sheet_name='USA Details')
        cross_usa = usa_df[usa_df['Plant Name'].str.contains('Cross', na=False)]
        
        if not cross_usa.empty:
            print(f"  Found {len(cross_usa)} units in USA Details")
            
            # Show all columns that might contain coal type info
            coal_related_cols = [col for col in usa_df.columns if any(word in col.lower() for word in ['coal', 'fuel', 'type'])]
            print(f"  Coal/fuel related columns: {coal_related_cols}")
            
            # Show data for each unit
            for idx, row in cross_usa.iterrows():
                unit = row.get('Unit', 'N/A')
                print(f"  Unit {unit}:")
                for col in coal_related_cols:
                    if col in row and pd.notna(row[col]):
                        print(f"    {col}: {row[col]}")
        
        # Check Full Names sheet for coal type mappings
        print(f"\n📊 Checking Full Names sheet:")
        full_names_df = pd.read_excel(excel_file, sheet_name='Full Names')
        print(f"  Columns: {list(full_names_df.columns)}")
        
        # Show coal type mappings
        if 'Coal Type' in full_names_df.columns:
            print(f"  Coal types available:")
            for idx, row in full_names_df.iterrows():
                coal_type = row.get('Coal Type')
                if pd.notna(coal_type):
                    print(f"    {coal_type}")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    check_excel_coal_types()
