#!/usr/bin/env python3
"""
Check the actual USA Details sheet for unit information
"""

import pandas as pd

def check_usa_details_sheet():
    """Check the USA Details sheet specifically"""
    print("🔍 CHECKING USA DETAILS SHEET FOR UNIT INFORMATION")
    print("=" * 60)
    
    try:
        usa_file = "/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH-feat-agi-tech-int-007/USA Details.xlsx"
        
        # Get all sheet names first
        xl_file = pd.ExcelFile(usa_file)
        print(f"📋 Available sheets: {xl_file.sheet_names}")
        
        # Check if there's a "USA Details" sheet
        if "USA Details" in xl_file.sheet_names:
            df_details = pd.read_excel(usa_file, sheet_name="USA Details")
            
            print(f"\n📊 USA Details sheet shape: {df_details.shape}")
            print(f"📋 Column names: {list(df_details.columns)}")
            
            # Filter for Antelope Valley
            antelope_records = df_details[df_details['Plant Name'].str.contains('Antelope Valley', na=False)]
            
            print(f"\n📊 Found {len(antelope_records)} records for Antelope Valley in USA Details")
            
            if len(antelope_records) > 0:
                print("\n📋 Antelope Valley records from USA Details sheet:")
                for idx, record in antelope_records.iterrows():
                    print(f"\n   Record {idx}:")
                    for col in df_details.columns:
                        value = record.get(col, 'N/A')
                        print(f"     {col}: {value}")
                        
                # Look for unit ID and capacity columns
                unit_id_cols = [col for col in df_details.columns if 'unit' in col.lower() and 'id' in col.lower()]
                capacity_cols = [col for col in df_details.columns if 'capacity' in col.lower() or 'mw' in col.lower()]
                
                print(f"\n🔍 Unit ID columns: {unit_id_cols}")
                print(f"🔍 Capacity columns: {capacity_cols}")
                
            else:
                print("❌ No Antelope Valley records found in USA Details sheet")
        else:
            print("❌ No 'USA Details' sheet found")
            print("Available sheets:", xl_file.sheet_names)
        
    except Exception as e:
        print(f"❌ Failed to check USA Details sheet: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_usa_details_sheet()
