#!/usr/bin/env python3
"""
Detailed Plant Calculations Test
===============================

Shows detailed calculations for:
1. Antelope Valley Station
2. Dry Fork Station  
3. Laramie River Station

Displays every variable, assumption, and calculation step.
"""

import sys
import json
from datetime import datetime
sys.path.append('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH-feat-agi-tech-int-007')

from usa_excel_calculation_engine import USAExcelCalculationEngine
from excel_power_plant_tool import ExcelPowerPlantTool
import pandas as pd

def detailed_plant_analysis(plant_name: str):
    """Perform detailed analysis of a single plant"""
    print(f"\n{'='*100}")
    print(f"🔍 DETAILED ANALYSIS: {plant_name}")
    print(f"{'='*100}")
    
    results = {
        "plant_name": plant_name,
        "timestamp": datetime.now().isoformat(),
        "analysis": {}
    }
    
    try:
        # Initialize engines
        print("🔧 Initializing calculation engines...")
        usa_engine = USAExcelCalculationEngine(
            '/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH-feat-agi-tech-int-007-feat-agi-tech-int-007/USA Details.xlsx',
            '/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH-feat-agi-tech-int-007-feat-agi-tech-int-007/current state calculations.xlsx'
        )
        excel_tool = ExcelPowerPlantTool('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH-feat-agi-tech-int-007-feat-agi-tech-int-007/USA Details.xlsx', 'test')
        print("✅ Engines initialized")
        
        # STEP 1: Plant Capacity Analysis
        print(f"\n📊 STEP 1: PLANT CAPACITY ANALYSIS")
        print("-" * 60)
        
        plant_capacity = usa_engine._calculate_plant_capacity_from_excel(plant_name)
        print(f"Total Plant Capacity: {plant_capacity} MW")
        
        results["analysis"]["plant_capacity"] = {
            "total_mw": plant_capacity,
            "source": "USA Details.xlsx - sum of all unit capacities"
        }
        
        # STEP 2: Unit Details
        print(f"\n📊 STEP 2: UNIT DETAILS")
        print("-" * 60)
        
        unit_data = excel_tool.get_plant_data(plant_name)
        if unit_data:
            print(f"Found {len(unit_data)} units:")
            results["analysis"]["units"] = {}
            
            for i, unit in enumerate(unit_data, 1):
                unit_id = unit.get('unit_id', f'Unit_{i}')
                capacity = unit.get('capacity', 0)
                technology = unit.get('technology', 'Unknown')
                fuel_type = unit.get('fuel_type', 'Unknown')
                
                print(f"  Unit {unit_id}: {capacity} MW, {technology}, {fuel_type}")
                
                results["analysis"]["units"][unit_id] = {
                    "capacity_mw": capacity,
                    "technology": technology,
                    "fuel_type": fuel_type
                }
        else:
            print("❌ No unit data found")
            results["analysis"]["units"] = {}
        
        # STEP 3: Excel Data Extraction
        print(f"\n📊 STEP 3: EXCEL DATA EXTRACTION")
        print("-" * 60)
        
        excel_data = usa_engine.extract_plant_data_from_usa_excel(plant_name)
        if excel_data:
            years = list(excel_data.keys())
            print(f"Excel data available for years: {years}")
            
            results["analysis"]["excel_data"] = {
                "years_available": years,
                "yearly_data": {}
            }
            
            # Analyze each year
            for year in years:
                year_data = excel_data[year]
                if isinstance(year_data, list):
                    total_net_gen = 0
                    fuel_types = set()
                    total_fuel_consumption = 0
                    
                    for record in year_data:
                        net_gen = record.get('Net Generation (Megawatthours)', 0)
                        fuel = record.get('Fuel', 'Unknown')
                        fuel_consumption = record.get('Electric Fuel Consumption Quantity', 0)
                        
                        if isinstance(net_gen, (int, float)):
                            total_net_gen += net_gen
                        if isinstance(fuel_consumption, (int, float)):
                            total_fuel_consumption += fuel_consumption
                        fuel_types.add(fuel)
                    
                    print(f"  {year}:")
                    print(f"    Net Generation: {total_net_gen:,.0f} MWh")
                    print(f"    Fuel Types: {list(fuel_types)}")
                    print(f"    Fuel Consumption: {total_fuel_consumption:,.0f} tons")
                    
                    results["analysis"]["excel_data"]["yearly_data"][year] = {
                        "net_generation_mwh": total_net_gen,
                        "fuel_types": list(fuel_types),
                        "fuel_consumption_tons": total_fuel_consumption
                    }
        else:
            print("❌ No Excel data found")
            results["analysis"]["excel_data"] = {}
        
        # STEP 4: Detailed Calculations (using first unit as example)
        if unit_data and len(unit_data) > 0:
            print(f"\n📊 STEP 4: DETAILED CALCULATIONS (First Unit)")
            print("-" * 60)
            
            first_unit = unit_data[0]
            unit_capacity = first_unit.get('capacity', 0)
            technology = first_unit.get('technology', 'subcritical')
            
            # Auxiliary Power Calculation
            print(f"\n🔧 AUXILIARY POWER CALCULATION:")
            aux_power = usa_engine.get_auxiliary_power_percentage(unit_capacity, technology)
            print(f"  Input: Capacity = {unit_capacity} MW, Technology = {technology}")
            print(f"  Method: Lookup table based on capacity and technology")
            print(f"  Result: {aux_power*100:.1f}% (decimal: {aux_power})")
            
            # GCV Calculation
            print(f"\n🔧 GCV CALCULATION:")
            coal_type = "bituminous"  # Default assumption
            gcv = usa_engine.get_gcv_for_coal_type(coal_type)
            print(f"  Coal Type: {coal_type} (assumed)")
            print(f"  Method: Hardcoded values from Assumptions sheet")
            print(f"  Result: {gcv} kcal/kg")
            
            # Gross Generation Calculation
            print(f"\n🔧 GROSS GENERATION CALCULATION:")
            if excel_data and '2024' in excel_data:
                net_gen_2024 = results["analysis"]["excel_data"]["yearly_data"].get("2024", {}).get("net_generation_mwh", 0)
                if net_gen_2024 > 0:
                    gross_gen_2024 = net_gen_2024 / (1 - aux_power)
                    print(f"  Formula: Gross = Net / (1 - Aux%)")
                    print(f"  Input: Net = {net_gen_2024:,.0f} MWh, Aux = {aux_power}")
                    print(f"  Calculation: {net_gen_2024:,.0f} / (1 - {aux_power}) = {gross_gen_2024:,.0f} MWh")
                    
                    # PLF Calculation
                    print(f"\n🔧 PLF CALCULATION:")
                    max_generation = unit_capacity * 8760
                    plf = gross_gen_2024 / max_generation
                    print(f"  Formula: PLF = Gross / (Capacity × 8760)")
                    print(f"  Input: Gross = {gross_gen_2024:,.0f} MWh, Capacity = {unit_capacity} MW")
                    print(f"  Calculation: {gross_gen_2024:,.0f} / ({unit_capacity} × 8760) = {plf:.3f} = {plf*100:.1f}%")
                    
                    results["analysis"]["calculations"] = {
                        "auxiliary_power_percent": aux_power * 100,
                        "gcv_kcal_per_kg": gcv,
                        "net_generation_2024_mwh": net_gen_2024,
                        "gross_generation_2024_mwh": gross_gen_2024,
                        "plf_2024_percent": plf * 100,
                        "max_annual_generation_mwh": max_generation
                    }
                else:
                    print("  ❌ No 2024 net generation data available")
            else:
                print("  ❌ No 2024 Excel data available")
        
        print(f"\n✅ Analysis completed for {plant_name}")
        return results
        
    except Exception as e:
        print(f"❌ Error analyzing {plant_name}: {e}")
        import traceback
        traceback.print_exc()
        results["error"] = str(e)
        return results

def main():
    """Run detailed analysis for all test plants"""
    print("🚀 DETAILED PLANT CALCULATIONS TEST")
    print("=" * 100)
    
    test_plants = [
        "Antelope Valley Station",
        "Dry Fork Station",
        "Laramie River Station"
    ]
    
    all_results = {
        "test_info": {
            "timestamp": datetime.now().isoformat(),
            "description": "Detailed plant calculations showing every variable and assumption"
        },
        "plants": {}
    }
    
    for plant_name in test_plants:
        plant_results = detailed_plant_analysis(plant_name)
        all_results["plants"][plant_name] = plant_results
    
    # Save results
    output_file = "detailed_plant_calculations_results.json"
    with open(output_file, 'w') as f:
        json.dump(all_results, f, indent=2, default=str)
    
    print(f"\n{'='*100}")
    print("🎯 DETAILED CALCULATIONS SUMMARY")
    print(f"{'='*100}")
    
    for plant_name in test_plants:
        plant_data = all_results["plants"][plant_name]
        if "error" not in plant_data:
            capacity = plant_data.get("analysis", {}).get("plant_capacity", {}).get("total_mw", 0)
            unit_count = len(plant_data.get("analysis", {}).get("units", {}))
            print(f"✅ {plant_name}: {capacity} MW, {unit_count} units")
        else:
            print(f"❌ {plant_name}: Error occurred")
    
    print(f"\n📄 Detailed results saved to: {output_file}")
    print("🔍 Review the JSON file for complete calculation details")

if __name__ == "__main__":
    main()
