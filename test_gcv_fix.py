#!/usr/bin/env python3
"""
Test the GCV fix for Lignite coal
"""

import sys
sys.path.append('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH-feat-agi-tech-int-007')
sys.path.append('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH-feat-agi-tech-int-007/backend/src')

from excel_power_plant_tool import ExcelPowerPlantTool
from agent.unit_extraction_stages import combine_unit_data

def test_gcv_fix():
    """Test that GCV is now correct for Lignite coal"""
    
    print("🔧 TESTING GCV FIX FOR LIGNITE COAL")
    print("=" * 60)
    
    try:
        excel_tool = ExcelPowerPlantTool('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH-feat-agi-tech-int-007/USA Details.xlsx')
        plant_name = "Antelope Valley"
        unit_num = "2"  # Test Unit 2 which has Lignite coal
        
        # Get Excel data
        excel_results = excel_tool.get_plant_data(plant_name, unit_num)
        
        if excel_results:
            excel_data = excel_results[0]
            
            # Check coal type in Excel data
            fuel_types = excel_data.get('fuel_type', [])
            if fuel_types:
                coal_type = fuel_types[0].get('type', 'Unknown')
                print(f"📊 Coal type from Excel: {coal_type}")
            
            # Create plant context
            plant_context = {
                'excel_data': excel_data,
                'plant_name': plant_name,
                'country': 'United States',
                'plant_uid': f'test-gcv-fix-{unit_num}',
                'plant_type': 'coal'
            }
            
            # Process unit data
            print(f"\n🔧 Processing Unit {unit_num} with GCV fix...")
            combined_data = combine_unit_data([], unit_num, plant_context)
            
            # Check results
            gcv_coal = combined_data.get('gcv_coal')
            fuel_type_result = combined_data.get('fuel_type', [])
            
            print(f"\n📊 RESULTS:")
            print(f"  GCV Coal: {gcv_coal} kcal/kg")
            
            if fuel_type_result:
                coal_type_result = fuel_type_result[0].get('type', 'Unknown')
                print(f"  Coal type in result: {coal_type_result}")
                
                # Check if GCV is correct for coal type
                expected_gcv = {
                    'Lignite': 3350,
                    'Bituminous': 6690,
                    'Sub-bituminous': 4900,
                    'Anthracite': 6690  # Default to bituminous
                }
                
                expected = expected_gcv.get(coal_type_result, 6690)
                
                print(f"\n🔍 VALIDATION:")
                print(f"  Expected GCV for {coal_type_result}: {expected} kcal/kg")
                print(f"  Actual GCV: {gcv_coal} kcal/kg")
                
                if gcv_coal == expected:
                    print(f"  ✅ SUCCESS: GCV is correct for {coal_type_result} coal!")
                    return True
                else:
                    print(f"  ❌ FAILURE: GCV is still wrong for {coal_type_result} coal")
                    print(f"     Expected: {expected}, Got: {gcv_coal}")
                    return False
            else:
                print(f"  ❌ No fuel type data found in result")
                return False
        
        else:
            print(f"❌ No Excel data found for {plant_name} Unit {unit_num}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_gcv_fix()
    if success:
        print("\n🎉 GCV FIX SUCCESSFUL!")
    else:
        print("\n❌ GCV FIX FAILED - NEEDS MORE WORK")
