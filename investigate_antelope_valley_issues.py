#!/usr/bin/env python3
"""
Investigate the actual issues with Antelope Valley Station
"""

import sys
sys.path.append('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH-feat-agi-tech-int-007')
sys.path.append('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH-feat-agi-tech-int-007/backend/src')

from excel_power_plant_tool import ExcelPowerPlantTool
from agent.unit_extraction_stages import combine_unit_data
import pandas as pd

def investigate_antelope_valley_issues():
    """Investigate the real issues with Antelope Valley Station"""
    
    print("🔍 INVESTIGATING ANTELOPE VALLEY STATION ISSUES")
    print("=" * 80)
    print("User is correct - I was testing wrong plant!")
    print("Let me check Antelope Valley Station properly...")
    print()
    
    try:
        excel_tool = ExcelPowerPlantTool('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH-feat-agi-tech-int-007/USA Details.xlsx')
        plant_name = "Antelope Valley"
        
        # First, check how many units Antelope Valley has
        print("📊 CHECKING ANTELOPE VALLEY UNITS IN EXCEL:")
        print("-" * 50)
        
        # Check Excel data for all possible units
        for unit_num in ['1', '2', '3']:
            excel_results = excel_tool.get_plant_data(plant_name, unit_num)
            if excel_results:
                print(f"  ✅ Unit {unit_num}: Found in Excel")
            else:
                print(f"  ❌ Unit {unit_num}: Not found in Excel")
        
        print()
        
        # Test Unit 1 and Unit 2 processing
        for unit_num in ['1', '2']:
            print(f"📊 TESTING ANTELOPE VALLEY UNIT {unit_num}:")
            print("-" * 40)
            
            excel_results = excel_tool.get_plant_data(plant_name, unit_num)
            
            if excel_results:
                excel_data = excel_results[0]
                
                print(f"  Excel data found for Unit {unit_num}")
                print(f"  Capacity: {excel_data.get('capacity')} MW")
                
                # Check fuel type in Excel data
                fuel_types = excel_data.get('fuel_type', [])
                if fuel_types:
                    coal_type = fuel_types[0].get('type', 'Unknown')
                    print(f"  Coal type from Excel: {coal_type}")
                
                # Create plant context
                plant_context = {
                    'excel_data': excel_data,
                    'plant_name': plant_name,
                    'country': 'United States',
                    'plant_uid': f'test-antelope-{unit_num}',
                    'plant_type': 'coal'
                }
                
                # Process unit data
                combined_data = combine_unit_data([], unit_num, plant_context)
                
                # Check the issues user reported
                gcv_coal = combined_data.get('gcv_coal')
                heat_rate = combined_data.get('heat_rate')
                efficiency = combined_data.get('coal_unit_efficiency')
                plf = combined_data.get('plf', [])
                auxiliary_power = combined_data.get('auxiliary_power_consumed', [])
                gross_power = combined_data.get('gross_power_generation', [])
                fuel_type_combined = combined_data.get('fuel_type', [])
                
                print(f"  📊 RESULTS:")
                print(f"    GCV Coal: {gcv_coal} kcal/kg")
                print(f"    Heat Rate: {heat_rate} kcal/kWh")
                print(f"    Efficiency: {efficiency}%")
                print(f"    PLF records: {len(plf)}")
                print(f"    Auxiliary power records: {len(auxiliary_power)}")
                print(f"    Gross power records: {len(gross_power)}")
                
                if fuel_type_combined:
                    coal_type_combined = fuel_type_combined[0].get('type', 'Unknown')
                    print(f"    Coal type in result: {coal_type_combined}")
                
                # Check specific issues
                print(f"  🔍 ISSUE ANALYSIS:")
                
                # Issue 1: Empty arrays
                if len(plf) == 0:
                    print(f"    ❌ PLF array is empty - USER WAS RIGHT")
                else:
                    print(f"    ✅ PLF array has {len(plf)} records")
                
                if len(auxiliary_power) == 0:
                    print(f"    ❌ Auxiliary power array is empty - USER WAS RIGHT")
                else:
                    print(f"    ✅ Auxiliary power array has {len(auxiliary_power)} records")
                
                if len(gross_power) == 0:
                    print(f"    ❌ Gross power array is empty - USER WAS RIGHT")
                else:
                    print(f"    ✅ Gross power array has {len(gross_power)} records")
                
                # Issue 2: GCV for Lignite
                if fuel_type_combined:
                    coal_type_combined = fuel_type_combined[0].get('type', 'Unknown')
                    if coal_type_combined.lower() == 'lignite':
                        if gcv_coal == 3350:
                            print(f"    ✅ GCV {gcv_coal} is correct for Lignite")
                        else:
                            print(f"    ❌ GCV {gcv_coal} is WRONG for Lignite - should be 3350 - USER WAS RIGHT")
                    else:
                        print(f"    ⚠️ Coal type is {coal_type_combined}, not Lignite")
                
                # Issue 3: Heat rate calculation
                if efficiency and heat_rate:
                    eff_decimal = efficiency / 100 if efficiency > 1 else efficiency
                    expected_heat_rate = 860.42 / eff_decimal
                    
                    if abs(expected_heat_rate - heat_rate) < 5:
                        print(f"    ✅ Heat rate {heat_rate} is correct")
                    else:
                        print(f"    ❌ Heat rate {heat_rate} is WRONG - should be {expected_heat_rate:.1f} - USER WAS RIGHT")
                
                print()
            
            else:
                print(f"  ❌ No Excel data found for Unit {unit_num}")
                print()
        
        # Check why only Unit 2 JSON was created
        print("🔍 CHECKING UNIT PROCESSING LOGIC:")
        print("-" * 50)
        print("User reported: 'I have only one JSON in my bucket'")
        print("User reported: 'it gave only one unit JSON even though the input station has 2 units'")
        print("This suggests the unit processing loop is not working correctly")
        print()
        
        # Check if there are any other Antelope Valley JSON files
        import os
        json_files = [f for f in os.listdir('.') if 'antelope' in f.lower() and f.endswith('.json')]
        print(f"Antelope Valley JSON files found: {json_files}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

def check_lignite_gcv_calculation():
    """Check why Lignite coal is getting wrong GCV"""
    
    print("\n🔧 CHECKING LIGNITE GCV CALCULATION:")
    print("-" * 50)
    
    try:
        from agent.power_plant_calculation_engine import CalculationConstants
        
        constants = CalculationConstants()
        
        print("Coal properties from calculation engine:")
        for coal_type, props in constants.COAL_PROPERTIES.items():
            print(f"  {coal_type}: GCV = {props['gcv']} kcal/kg")
        
        # Test GCV lookup for lignite
        from agent.power_plant_calculation_engine import create_calculation_engine
        calc_engine = create_calculation_engine()
        
        lignite_gcv = calc_engine._get_coal_gcv('lignite')
        print(f"\nLignite GCV lookup result: {lignite_gcv} kcal/kg")
        
        if lignite_gcv == 3350:
            print("✅ Lignite GCV lookup is correct")
        else:
            print("❌ Lignite GCV lookup is wrong")
        
    except Exception as e:
        print(f"❌ Error checking GCV: {e}")

if __name__ == "__main__":
    investigate_antelope_valley_issues()
    check_lignite_gcv_calculation()
