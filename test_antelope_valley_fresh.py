#!/usr/bin/env python3
"""
Fresh test of Antelope Valley Station to see if current system produces correct results
"""

import sys
import os
sys.path.append('backend/src')

from agent.graph import run_power_plant_extraction
import asyncio

async def test_antelope_valley_fresh():
    """Run a fresh test of Antelope Valley Station"""
    
    print("🧪 FRESH TEST: ANTELOPE VALLEY STATION")
    print("=" * 60)
    
    # Test with Antelope Valley Station
    plant_name = "Antelope Valley Station"
    
    print(f"🏭 Testing: {plant_name}")
    print("🔄 Running full extraction pipeline...")
    print()
    
    try:
        # Run the full extraction pipeline
        result = await run_power_plant_extraction(plant_name)
        
        if result and result.get('success'):
            print("✅ Extraction completed successfully!")
            
            # Check if unit JSONs were created
            unit_files = []
            for i in range(1, 5):  # Check for units 1-4
                unit_file = f"unit#coal#{i}#plant#1.json"
                if os.path.exists(unit_file):
                    unit_files.append(unit_file)
            
            print(f"📄 Unit JSON files found: {unit_files}")
            
            # Check the content of each unit file
            for unit_file in unit_files:
                print(f"\n📊 CHECKING {unit_file}:")
                print("-" * 40)
                
                try:
                    import json
                    with open(unit_file, 'r') as f:
                        unit_data = json.load(f)
                    
                    # Check key fields
                    plf = unit_data.get('plf', [])
                    aux_power = unit_data.get('auxiliary_power_consumed', [])
                    gross_power = unit_data.get('gross_power_generation', [])
                    
                    print(f"PLF: {len(plf)} items")
                    if plf:
                        print(f"  Sample: {plf[0]}")
                    
                    print(f"Auxiliary Power: {len(aux_power)} items")
                    if aux_power:
                        print(f"  Sample: {aux_power[0]}")
                    
                    print(f"Gross Power Generation: {len(gross_power)} items")
                    if gross_power:
                        print(f"  Sample: {gross_power[0]}")
                    
                    # Check GCV
                    gcv = unit_data.get('gcv_coal')
                    coal_type = unit_data.get('type')  # Coal type
                    print(f"Coal Type: {coal_type}")
                    print(f"GCV Coal: {gcv}")
                    
                except Exception as e:
                    print(f"❌ Error reading {unit_file}: {e}")
        
        else:
            print("❌ Extraction failed!")
            if result:
                print(f"Error: {result.get('error', 'Unknown error')}")
    
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_antelope_valley_fresh())
