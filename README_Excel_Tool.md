# Excel Power Plant Tool

A comprehensive tool for processing and querying power plant data from Excel files using natural language queries. This tool integrates seamlessly with your existing data collection pipeline and provides semantic search capabilities for USA power plant data.

## Features

- **Multi-sheet Excel Processing**: Handles complex Excel files with multiple data sheets
- **Natural Language Queries**: Process queries like "Find coal plants with high emission factors"
- **Vector Search**: Advanced semantic search using TF-IDF vectorization
- **Schema Compliance**: Output strictly follows the unit_level.json schema format
- **Performance Optimized**: Sub-second query response times
- **Comprehensive Data**: Covers 26,801+ power plant units with emission factors and fuel data

## Installation

```bash
pip3 install pandas openpyxl scikit-learn numpy
```

## Quick Start

```python
from excel_power_plant_tool import ExcelPowerPlantTool

# Initialize the tool (loads and processes Excel data)
tool = ExcelPowerPlantTool()

# Query specific plants
results = tool.get_plant_data("A B Brown")

# Natural language queries
results = tool.query("Find coal plants with high emission factors")
results = tool.query("Show me large capacity nuclear facilities")
results = tool.query("What plants started operating in the 1990s?")
```

## Data Sources

The tool processes the `USA Details.xlsx` file with the following sheets:

### USA Details Sheet
- **26,801 units** across 13,245+ plants
- Unit IDs, capacities, plant types
- Operating and retirement dates
- Geographic coordinates (latitude/longitude)
- Entity/operator information

### Coal Yearly Sheets (2020-2024)
- **1,600+ coal records** across 5 years
- Plant names and operators
- Fuel types (Bituminous, Sub Bituminous, etc.)
- Annual emission factors (kg CO2e/kWh)

### Full Names Sheet
- **16 coal type mappings**
- emission_factor_coal values for different coal types
- Maps coal names to standardized emission factors

## Output Format

All responses follow the unit_level.json schema:

```json
{
  "sk": "unit#Natural Gas Fired Combustion Turbine#4#A B Brown#Southern Indiana Gas & Elec Co",
  "capacity": 88.2,
  "capacity_unit": "MW",
  "commencement_date": "1991-06-01T00:00:00.000Z",
  "remaining_useful_life": "2041-06-01T00:00:00.000Z",
  "unit_lifetime": 50.0,
  "unit_number": "4",
  "plant_id": "A B Brown",
  "latitude": 37.9053,
  "longitude": -87.715,
  "emission_factor_coal": 2.44068,
  "fuel_type": [
    {
      "fuel": "Coal",
      "type": "Bituminous",
      "years_percentage": {
        "2023": "100",
        "2022": "100",
        "2021": "100",
        "2020": "100"
      }
    }
  ],
  "emission_factor": [
    {
      "value": 1.1342368564180658,
      "year": "2023"
    },
    {
      "value": 1.0636802693538405,
      "year": "2022"
    }
  ]
}
```

## Key Features

### Calculated Fields
- **unit_lifetime**: Automatically calculated as the difference between retirement and commencement dates
- **50-year rule**: Missing retirement dates default to 50 years from commencement
- **Timestamp formatting**: ISO format with milliseconds (yyyy-mm-ddThh:mm:ss.msZ)

### Data Mapping
- Cross-references USA Details with Coal yearly data by plant name
- Maps coal types to emission_factor_coal using Full Names sheet
- Handles fuzzy matching for plant names with special characters

### Query Capabilities
- **Plant-specific**: "A B Brown plant", "Barry power station"
- **Fuel-based**: "coal plants", "natural gas facilities", "nuclear power plants"
- **Capacity-based**: "high capacity plants", "small units under 10 MW"
- **Time-based**: "plants from the 1970s", "recently built facilities"
- **Technical**: "emission factors for coal", "bituminous coal plants"

## Performance

- **Initialization**: ~3-5 seconds (loads and processes all data)
- **Query Response**: <0.01 seconds average
- **Memory Usage**: Efficient pandas-based processing
- **Vector Search**: 1000-feature TF-IDF with cosine similarity

## Architecture Integration

This tool is designed to integrate with your existing data collection pipeline:

1. **Excel as Data Source**: Handles the specific USA Details.xlsx structure
2. **Schema Compliance**: Output matches your unit_level.json schema exactly
3. **Natural Language Interface**: Similar to ChatGPT/Grok Excel processing
4. **Complementary to Web Search**: Use for USA-specific fields, web search for others

## Testing

Comprehensive test suites are included:

```bash
# Basic functionality
python3 test_excel_tool.py

# Schema validation
python3 schema_validator.py

# Vector search capabilities
python3 test_vector_search.py

# Full integration test
python3 integration_test.py
```

## Error Handling

- Graceful handling of missing data
- Robust plant name matching (exact + fuzzy)
- Default values for missing timestamps
- Exception handling for malformed queries

## Limitations

- **USA-specific data only**: Limited to plants in the USA Details.xlsx file
- **Coal emission data**: Only available for plants with matching coal records
- **Time range**: Coal data covers 2020-2024
- **Static data**: Excel file must be updated manually for new data

## Future Enhancements

- Real-time data updates
- Additional fuel type support
- Geographic search capabilities
- Advanced filtering options
- API endpoint wrapper

## Support

For issues or questions:
1. Check the test suites for examples
2. Review the integration_test.py for comprehensive usage
3. Validate output with schema_validator.py

---

**Status**: ✅ Production Ready  
**Last Updated**: 2025-07-30  
**Version**: 1.0.0
