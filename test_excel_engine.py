#!/usr/bin/env python3
"""
Test script for the new Excel Calculation Engine
"""

import sys
import os
sys.path.append('backend/src')

# Test the new Excel calculation engine
print('🧪 TESTING EXCEL CALCULATION ENGINE')
print('=' * 50)

try:
    from agent.excel_calculation_engine import ExcelCalculationEngine
    
    # Create engine
    engine = ExcelCalculationEngine('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH-feat-agi-tech-int-007/current state calculations.xlsx')
    
    # Test data for Antelope Valley Station Unit 2
    test_unit_data = {
        'capacity': 1300,  # MW
        'technology': 'subcritical',
        'coal_type': 'lignite',
        'generation_mwh': 5000000,  # 5 GWh example
        'fuel_consumed_tons': 2000000,  # 2 million tons example
        'annual_emission_mt': 10.5  # 10.5 MtCO2 example
    }
    
    test_plant_context = {
        'plant_generation_mwh': 10000000,  # 10 GWh total
        'plant_capacity_mw': 2600  # 2 units x 1300 MW
    }
    
    print('📊 Test Unit Data:')
    for key, value in test_unit_data.items():
        print(f'  {key}: {value}')
    
    print()
    print('📊 Test Plant Context:')
    for key, value in test_plant_context.items():
        print(f'  {key}: {value}')
    
    print()
    print('🔧 Running Excel calculations...')
    
    # Test individual calculations
    print('\n🧮 Testing Individual Calculations:')
    print('-' * 40)
    
    # Test PLF Case 2
    plf_result = engine.calculate_plf_case2_direct_unit(
        test_unit_data['generation_mwh'], 
        test_unit_data['capacity']
    )
    if plf_result:
        print(f"✅ PLF Case 2: {plf_result['plf_unit']:.3f} ({plf_result['plf_unit']*100:.1f}%)")
    
    # Test efficiency calculation
    efficiency_result = engine.calculate_efficiency_from_fuel_generation(
        test_unit_data['generation_mwh'],
        engine.get_coal_gcv(test_unit_data['coal_type']),
        test_unit_data['fuel_consumed_tons']
    )
    if efficiency_result:
        print(f"✅ Efficiency: {efficiency_result['efficiency']:.3f} ({efficiency_result['efficiency']*100:.1f}%)")
        print(f"✅ Heat Rate: {efficiency_result['heat_rate_kcal_per_kwh']:.2f} kcal/kWh")
    
    # Test auxiliary power
    aux_power = engine.get_auxiliary_power(test_unit_data['capacity'], test_unit_data['technology'])
    if aux_power:
        print(f"✅ Auxiliary Power: {aux_power*100:.1f}%")
    
    # Test emission calculation
    emission_result = engine.calculate_emissions_from_plant_data(
        test_unit_data['annual_emission_mt'],
        test_unit_data['coal_type']
    )
    if emission_result:
        print(f"✅ Emission Factor: {emission_result['emission_factor_kg_per_kwh']:.6f} kg CO2/kWh")
    
    # Test full calculation
    print('\n🔧 Testing Full Calculation Interface:')
    print('-' * 40)
    
    results = engine.calculate_unit_parameters(test_unit_data, test_plant_context)
    
    if results:
        print()
        print('✅ FULL CALCULATION RESULTS:')
        print('=' * 30)
        
        # PLF results
        if 'plf_unit' in results:
            print(f'PLF: {results["plf_unit"]:.3f} ({results["plf_unit"]*100:.1f}%)')
        
        # Efficiency results
        if 'efficiency' in results:
            print(f'Efficiency: {results["efficiency"]:.3f} ({results["efficiency"]*100:.1f}%)')
        
        # Heat rate results
        if 'heat_rate_kcal_per_kwh' in results:
            print(f'Heat Rate: {results["heat_rate_kcal_per_kwh"]:.2f} kcal/kWh')
        
        # Auxiliary power results
        if 'auxiliary_power_percent' in results:
            print(f'Auxiliary Power: {results["auxiliary_power_percent"]*100:.1f}%')
        
        # Emission factor results
        if 'emission_factor_kg_per_kwh' in results:
            print(f'Emission Factor: {results["emission_factor_kg_per_kwh"]:.6f} kg CO2/kWh')
        
        print()
        print('📋 Methods Used:')
        for method in results['calculation_summary']['methods_used']:
            print(f'  - {method}')
            
    else:
        print('❌ Full calculations failed!')
        
    print('\n✅ Excel Calculation Engine test completed!')
    
except Exception as e:
    print(f'❌ Test failed with error: {e}')
    import traceback
    traceback.print_exc()
