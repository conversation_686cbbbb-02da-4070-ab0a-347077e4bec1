#!/usr/bin/env python3

import sys
import os
sys.path.append('.')

# Test the USA Excel calculation engine integration
from usa_excel_calculation_engine import create_usa_excel_calculation_engine
import json

def test_usa_calculations():
    """Test the USA Excel calculation engine with unit data"""

    print("🔧 TESTING USA EXCEL CALCULATION ENGINE INTEGRATION")
    print("=" * 60)

    try:
        # Initialize the USA Excel calculation engine
        calc_engine = create_usa_excel_calculation_engine()
        print("✅ USA Excel calculation engine initialized")

        # Test data for Antelope Valley Station Unit 1
        plant_name = "Antelope Valley"  # Remove "Station" as per the code logic
        unit_data = {
            'capacity': 477,
            'unit_number': '1',
            'technology': 'subcritical'
        }

        print(f"🔍 Testing calculations for: {plant_name}")
        print(f"📋 Unit: {unit_data['unit_number']}")
        print(f"📋 Capacity: {unit_data['capacity']} MW")
        print(f"📋 Technology: {unit_data['technology']}")

        # Run the USA calculations
        calc_results = calc_engine.calculate_unit_parameters_usa(plant_name, unit_data)

        if calc_results:
            print(f"✅ USA calculations successful!")

            # Check key fields
            key_fields = [
                'plf', 'auxiliary_power_consumed', 'gross_power_generation',
                'plf_unit', 'auxiliary_power_percent', 'calculation_method',
                'heat_rate_kcal_per_kwh', 'efficiency'
            ]

            print(f"\n📊 CALCULATION RESULTS:")
            print("-" * 40)

            for field in key_fields:
                value = calc_results.get(field, 'MISSING')
                if isinstance(value, list) and len(value) > 0:
                    # For time series data, show first value
                    first_value = value[0]
                    if isinstance(first_value, dict):
                        display_value = f"{first_value.get('value', 'N/A')} ({first_value.get('year', 'N/A')})"
                        print(f"  {field}: {display_value} [{len(value)} data points]")
                    else:
                        print(f"  {field}: {first_value} [{len(value)} items]")
                elif isinstance(value, (int, float)):
                    if 'plf' in field.lower() and value < 1:
                        print(f"  {field}: {value:.1%}")
                    elif 'auxiliary' in field.lower() and value < 1:
                        print(f"  {field}: {value:.1%}")
                    else:
                        print(f"  {field}: {value}")
                elif isinstance(value, str):
                    print(f"  {field}: {value}")
                else:
                    print(f"  {field}: {value}")

            # Show PLF data in detail
            plf_data = calc_results.get('plf', [])
            if plf_data:
                print(f"\n📈 PLF TIME SERIES DATA:")
                print("-" * 30)
                for item in plf_data:
                    if isinstance(item, dict):
                        value = item.get('value', 'N/A')
                        year = item.get('year', 'N/A')
                        if isinstance(value, (int, float)):
                            print(f"  {year}: {value:.1%}")
                        else:
                            print(f"  {year}: {value}")

            # Show auxiliary power data
            aux_data = calc_results.get('auxiliary_power_consumed', [])
            if aux_data:
                print(f"\n⚡ AUXILIARY POWER DATA:")
                print("-" * 30)
                for item in aux_data:
                    if isinstance(item, dict):
                        value = item.get('value', 'N/A')
                        year = item.get('year', 'N/A')
                        if isinstance(value, (int, float)):
                            print(f"  {year}: {value:.1%}")
                        else:
                            print(f"  {year}: {value}")

            # Show gross generation data
            gross_data = calc_results.get('gross_power_generation', [])
            if gross_data:
                print(f"\n🔋 GROSS GENERATION DATA:")
                print("-" * 30)
                for item in gross_data[:3]:  # Show first 3 years
                    if isinstance(item, dict):
                        value = item.get('value', 'N/A')
                        year = item.get('year', 'N/A')
                        print(f"  {year}: {value} GWh")

            # Check calculation summary
            calc_summary = calc_results.get('calculation_summary', {})
            if calc_summary:
                print(f"\n🔧 CALCULATION SUMMARY:")
                print("-" * 30)
                for key, value in calc_summary.items():
                    print(f"  {key}: {value}")

            print(f"\n🎯 OVERALL ASSESSMENT:")
            print("-" * 30)

            # Check if we have the critical fields
            missing_fields = []
            if not calc_results.get('plf'):
                missing_fields.append('PLF')
            if not calc_results.get('auxiliary_power_consumed'):
                missing_fields.append('Auxiliary Power')
            if not calc_results.get('gross_power_generation'):
                missing_fields.append('Gross Generation')

            if missing_fields:
                print(f"  ❌ Missing fields: {', '.join(missing_fields)}")
            else:
                print(f"  ✅ All critical fields present!")

            # Check data source
            data_source = calc_results.get('data_source', '')
            if 'USA Details.xlsx' in data_source:
                print(f"  ✅ Using real USA Excel data")
            else:
                print(f"  ⚠️ Data source: {data_source}")

        else:
            print(f"❌ USA calculations failed!")

    except Exception as e:
        print(f"❌ Error during calculations: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_usa_calculations()
