#!/usr/bin/env python3

import sys
import os
sys.path.append('.')
sys.path.append('./backend/src/agent')

# Test the complete unit extraction pipeline end-to-end
import json
import requests
from datetime import datetime

def test_complete_unit_extraction():
    """Test the complete unit extraction pipeline for Antelope Valley Station"""
    
    print("🔧 TESTING COMPLETE UNIT EXTRACTION PIPELINE - END TO END")
    print("=" * 70)
    
    # Test data for Antelope Valley Station
    test_data = {
        "plant_name": "Antelope Valley Station",
        "extraction_levels": ["unit"]  # Only extract units for this test
    }

    print(f"🔍 Testing complete pipeline for: {test_data['plant_name']}")
    print(f"📋 Extraction levels: {test_data['extraction_levels']}")

    try:
        # Make request to the FastAPI endpoint
        base_url = "http://localhost:8000"
        endpoint = f"{base_url}/api/v1/extraction/single-plant"
        
        print(f"🌐 Making request to: {endpoint}")
        
        response = requests.post(
            endpoint,
            json=test_data,
            headers={"Content-Type": "application/json"},
            timeout=30  # 30 second timeout for job submission
        )

        if response.status_code == 200:
            result = response.json()
            print(f"✅ Job submitted successfully!")
            print(f"📊 Response keys: {list(result.keys())}")

            # Get job ID
            job_id = result.get('job_id', 'N/A')
            print(f"🔑 Job ID: {job_id}")

            if job_id == 'N/A':
                print(f"❌ No job ID returned!")
                return

            # Poll for job completion
            print(f"⏳ Waiting for job completion...")
            status_endpoint = f"{base_url}/api/v1/status/{job_id}"
            results_endpoint = f"{base_url}/api/v1/results/{job_id}"

            max_wait_time = 600  # 10 minutes
            poll_interval = 10   # 10 seconds
            elapsed_time = 0

            while elapsed_time < max_wait_time:
                status_response = requests.get(status_endpoint, timeout=10)

                if status_response.status_code == 200:
                    status_data = status_response.json()
                    status = status_data.get('status', 'unknown')
                    progress = status_data.get('progress', 0)

                    print(f"📊 Status: {status} ({progress}%)")

                    if status == 'completed':
                        print(f"✅ Job completed successfully!")

                        # Get results
                        results_response = requests.get(results_endpoint, timeout=30)
                        if results_response.status_code == 200:
                            results_data = results_response.json()
                            units_data = results_data.get('units', [])
                            print(f"📊 Number of units extracted: {len(units_data)}")
                            break
                        else:
                            print(f"❌ Failed to get results: {results_response.status_code}")
                            return

                    elif status == 'failed':
                        error_msg = status_data.get('error', 'Unknown error')
                        print(f"❌ Job failed: {error_msg}")
                        return

                    elif status in ['running', 'pending']:
                        import time
                        time.sleep(poll_interval)
                        elapsed_time += poll_interval
                    else:
                        print(f"⚠️ Unknown status: {status}")
                        import time
                        time.sleep(poll_interval)
                        elapsed_time += poll_interval
                else:
                    print(f"❌ Failed to get status: {status_response.status_code}")
                    return

            if elapsed_time >= max_wait_time:
                print(f"⏰ Job timed out after {max_wait_time} seconds")
                return
            
            if units_data:
                print(f"\n📋 UNIT EXTRACTION RESULTS:")
                print("-" * 50)
                
                for i, unit in enumerate(units_data):
                    unit_number = unit.get('unit_number', f'Unit {i+1}')
                    capacity = unit.get('capacity', 'N/A')
                    technology = unit.get('technology', 'N/A')
                    
                    print(f"  🔧 {unit_number}:")
                    print(f"    Capacity: {capacity} MW")
                    print(f"    Technology: {technology}")
                    
                    # Check critical fields
                    critical_fields = ['plf', 'auxiliary_power_consumed', 'gross_power_generation', 'heat_rate_kcal_per_kwh', 'coal_unit_efficiency']
                    
                    print(f"    Critical Fields:")
                    for field in critical_fields:
                        value = unit.get(field, 'MISSING')
                        if isinstance(value, list) and len(value) > 0:
                            first_value = value[0]
                            if isinstance(first_value, dict):
                                display_value = f"{first_value.get('value', 'N/A')} ({first_value.get('year', 'N/A')})"
                                print(f"      {field}: {display_value} [{len(value)} points]")
                            else:
                                print(f"      {field}: {first_value} [{len(value)} items]")
                        elif isinstance(value, (int, float)):
                            if field in ['coal_unit_efficiency'] and value < 1:
                                print(f"      {field}: {value:.1%}")
                            else:
                                print(f"      {field}: {value}")
                        else:
                            print(f"      {field}: {value}")
                    
                    # Check S3 storage
                    s3_url = unit.get('s3_url', '')
                    if s3_url:
                        print(f"    ✅ S3 URL: {s3_url}")
                    else:
                        print(f"    ⚠️ No S3 URL found")
                    
                    print()
                
                # Overall assessment
                print(f"🎯 OVERALL ASSESSMENT:")
                print("-" * 30)
                
                # Check if all units have critical data
                units_with_plf = sum(1 for unit in units_data if unit.get('plf'))
                units_with_aux_power = sum(1 for unit in units_data if unit.get('auxiliary_power_consumed'))
                units_with_gross_gen = sum(1 for unit in units_data if unit.get('gross_power_generation'))
                units_with_efficiency = sum(1 for unit in units_data if unit.get('coal_unit_efficiency'))
                units_with_heat_rate = sum(1 for unit in units_data if unit.get('heat_rate_kcal_per_kwh'))
                
                total_units = len(units_data)
                
                print(f"  📊 Units with PLF data: {units_with_plf}/{total_units}")
                print(f"  📊 Units with Auxiliary Power: {units_with_aux_power}/{total_units}")
                print(f"  📊 Units with Gross Generation: {units_with_gross_gen}/{total_units}")
                print(f"  📊 Units with Efficiency: {units_with_efficiency}/{total_units}")
                print(f"  📊 Units with Heat Rate: {units_with_heat_rate}/{total_units}")
                
                if (units_with_plf == total_units and 
                    units_with_aux_power == total_units and 
                    units_with_gross_gen == total_units and
                    units_with_efficiency == total_units and
                    units_with_heat_rate == total_units):
                    print(f"  ✅ ALL CRITICAL FIELDS PRESENT FOR ALL UNITS!")
                else:
                    missing_fields = []
                    if units_with_plf < total_units:
                        missing_fields.append(f"PLF ({total_units - units_with_plf} missing)")
                    if units_with_aux_power < total_units:
                        missing_fields.append(f"Aux Power ({total_units - units_with_aux_power} missing)")
                    if units_with_gross_gen < total_units:
                        missing_fields.append(f"Gross Gen ({total_units - units_with_gross_gen} missing)")
                    if units_with_efficiency < total_units:
                        missing_fields.append(f"Efficiency ({total_units - units_with_efficiency} missing)")
                    if units_with_heat_rate < total_units:
                        missing_fields.append(f"Heat Rate ({total_units - units_with_heat_rate} missing)")
                    
                    print(f"  ⚠️ Missing fields: {', '.join(missing_fields)}")
                
                # Check if USA Excel calculations were used
                usa_calculations_used = any(
                    'USA Excel' in str(unit.get('calculation_summary', {})) or
                    'USA Excel' in str(unit.get('data_source', ''))
                    for unit in units_data
                )
                
                if usa_calculations_used:
                    print(f"  ✅ USA Excel calculations used successfully")
                else:
                    print(f"  ⚠️ USA Excel calculations may not have been used")
                
            else:
                print(f"❌ No units extracted!")
                
            # Check processing time
            processing_time = result.get('processing_time_seconds', 'N/A')
            print(f"⏱️ Processing time: {processing_time} seconds")
            
        else:
            print(f"❌ Request failed with status code: {response.status_code}")
            print(f"❌ Response: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print(f"❌ Connection error - is the FastAPI server running on localhost:8000?")
        print(f"💡 To start the server, run: cd backend && python -m uvicorn main:app --reload --port 8000")
        
    except requests.exceptions.Timeout:
        print(f"❌ Request timed out after 5 minutes")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_complete_unit_extraction()
