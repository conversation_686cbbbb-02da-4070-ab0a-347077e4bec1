#!/usr/bin/env python3
"""
Test the unit extraction loop directly to see debug logs
"""

import sys
import os
sys.path.append('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH-feat-agi-tech-int-007')

def test_unit_extraction_loop():
    """Test the unit extraction loop with debug logging"""
    print("🚀 TESTING UNIT EXTRACTION LOOP WITH DEBUG")
    print("=" * 80)
    
    try:
        # Import the graph functions
        from backend.src.agent.graph import extract_unit_data_fresh
        from backend.src.agent.state import OverallState
        
        # Create a mock state that simulates Antelope Valley
        mock_state = OverallState({
            "session_id": "debug-test-123",
            "plant_name": "Antelope Valley Station",
            "plant_name_for_s3": "Antelope Valley Station",
            "country": "United States",
            "plant_technology": "coal",
            "plant_type": "coal",
            "plant_uid": "test-plant-uuid-debug",
            "org_id": "test-org-debug",
            "extracted_units": ["1", "2"],  # Both units
            "unit_template": {},
            "no_units_found": False,
            "ready_for_unit_processing": True,
            "plant_data": {
                "plant_type": "coal",
                "plant_id": "1",
                "units_id": [1, 2]  # Both units detected
            },
            "messages": [],
            "excel_tool_used": False,
            "excel_units_data": []
        })
        
        print(f"📋 Mock state created:")
        print(f"   Plant: {mock_state.get('plant_name')}")
        print(f"   Units: {mock_state.get('extracted_units')}")
        print(f"   Plant data units_id: {mock_state.get('plant_data', {}).get('units_id')}")
        
        print(f"\n🔄 Calling extract_unit_data_fresh...")
        
        # Call the function that contains the unit processing loop
        result_state = extract_unit_data_fresh(mock_state)
        
        print(f"\n📋 FUNCTION RESULT:")
        print(f"   Result type: {type(result_state)}")
        print(f"   Result keys: {list(result_state.keys()) if isinstance(result_state, dict) else 'Not a dict'}")
        
        # Check if unit responses were created
        unit_responses = result_state.get("messages", [])
        print(f"   Unit responses: {len(unit_responses)}")
        
        print(f"\n✅ Unit extraction loop test completed")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_unit_extraction_loop()
