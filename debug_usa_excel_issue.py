#!/usr/bin/env python3

import sys
import os
sys.path.append('.')

from usa_excel_calculation_engine import USAExcelCalculationEngine
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_usa_excel_extraction():
    """Test the USA Excel calculation engine to debug the issue"""
    
    print("🔧 DEBUGGING USA EXCEL CALCULATION ENGINE")
    print("=" * 60)
    
    try:
        # Initialize the engine with correct paths
        usa_details_path = '/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH-feat-agi-tech-int-007/USA Details.xlsx'
        calculations_path = '/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH-feat-agi-tech-int-007/current state calculations.xlsx'

        engine = USAExcelCalculationEngine(usa_details_path, calculations_path)
        print("✅ USA Excel calculation engine initialized")
        
        # Test plant data extraction
        plant_name = "Antelope Valley"
        print(f"\n🔍 Testing plant data extraction for: {plant_name}")
        
        plant_data = engine.extract_plant_data_from_usa_excel(plant_name)
        
        if plant_data:
            print(f"✅ Plant data extracted successfully!")
            print(f"📊 Years with data: {list(plant_data.keys())}")
            
            # Show sample data for 2024
            if '2024' in plant_data:
                data_2024 = plant_data['2024']
                print(f"\n📅 2024 Data Sample:")
                print(f"   Total Net Generation: {data_2024.get('total_net_generation_mwh', 0):,.0f} MWh")
                print(f"   Total Fuel Consumption: {data_2024.get('total_fuel_consumption', 0):,.0f} units")
                print(f"   Fuel Types: {data_2024.get('fuel_types', [])}")
                print(f"   Emission Factors: {data_2024.get('emission_factors', [])}")
        else:
            print("❌ No plant data extracted!")
            print("🔍 Let's check what's happening...")
            
            # Test direct Excel access
            import pandas as pd
            excel_file = '/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH-feat-agi-tech-int-007/USA Details.xlsx'
            
            print(f"\n📋 Testing direct Excel access...")
            df_2024 = pd.read_excel(excel_file, sheet_name='Coal 2024')
            
            print(f"📊 Coal 2024 sheet shape: {df_2024.shape}")
            print(f"📋 Column names: {list(df_2024.columns)}")
            
            # Check for Antelope Valley
            antelope_mask = df_2024['Plant Name'].str.contains('Antelope Valley', na=False, case=False)
            antelope_records = df_2024[antelope_mask]
            
            print(f"\n📊 Found {len(antelope_records)} Antelope Valley records")
            
            if len(antelope_records) > 0:
                print("\n📋 Sample record:")
                record = antelope_records.iloc[0]
                for col in df_2024.columns:
                    value = record.get(col, 'N/A')
                    print(f"   {col}: {value}")
                    
                # Test the specific column access that's failing
                print(f"\n🔍 Testing column access:")
                net_gen_old = record.get('"Net Generation (Megawatthours)"', 'NOT_FOUND')
                net_gen_new = record.get('Net Generation (Megawatthours)', 'NOT_FOUND')
                fuel_cons_old = record.get('"Electric Fuel Consumption Quantity"', 'NOT_FOUND')
                fuel_cons_new = record.get('Electric Fuel Consumption Quantity', 'NOT_FOUND')
                
                print(f"   Old column name (with quotes) - Net Gen: {net_gen_old}")
                print(f"   New column name (no quotes) - Net Gen: {net_gen_new}")
                print(f"   Old column name (with quotes) - Fuel Cons: {fuel_cons_old}")
                print(f"   New column name (no quotes) - Fuel Cons: {fuel_cons_new}")
        
        # Test unit parameter calculation
        print(f"\n🔧 Testing unit parameter calculation...")
        unit_data = {
            'capacity': 477,
            'unit_number': '1',
            'technology': 'subcritical'
        }
        
        calc_results = engine.calculate_unit_parameters_usa(plant_name, unit_data)
        
        if calc_results:
            print(f"✅ Unit calculation successful!")
            print(f"📊 Results keys: {list(calc_results.keys())}")
            
            # Show key results
            plf_data = calc_results.get('plf', [])
            aux_power_data = calc_results.get('auxiliary_power_consumed', [])
            gross_gen_data = calc_results.get('gross_power_generation', [])
            
            print(f"📊 PLF data points: {len(plf_data)}")
            print(f"📊 Auxiliary power data points: {len(aux_power_data)}")
            print(f"📊 Gross generation data points: {len(gross_gen_data)}")
            
            if plf_data:
                print(f"📊 Sample PLF (2024): {plf_data[0].get('value', 0):.1%}")
                
        else:
            print("❌ Unit calculation failed!")
            
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_usa_excel_extraction()
