#!/usr/bin/env python3
"""
Test script to verify the refined coal and remaining useful life fixes
"""

import sys
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_refined_coal_fix():
    """Test that refined coal is zero except during generation years"""
    
    print("🔥 TESTING REFINED COAL FIX")
    print("=" * 50)
    
    try:
        from excel_power_plant_tool import ExcelPowerPlantTool
        
        # Create tool instance
        tool = ExcelPowerPlantTool()
        
        # Test with a known plant that has refined coal
        plant_name = "Antelope Valley Station"
        
        print(f"🌱 Testing plant: {plant_name}")
        
        # Get plant data
        result = tool.get_plant_data(plant_name)

        if result and isinstance(result, list) and len(result) > 0:
            print(f"✅ Found {len(result)} units")

            # Check each unit for refined coal
            for i, unit in enumerate(result, 1):
                print(f"\n📋 Unit {i}: {unit.get('unit_id', 'Unknown')}")
                
                # Check fuel types
                fuel_types = unit.get('fuel_type', [])
                print(f"   Fuel types: {len(fuel_types)}")
                
                for fuel in fuel_types:
                    fuel_name = fuel.get('fuel', 'Unknown')
                    fuel_type = fuel.get('type', 'Unknown')
                    
                    print(f"   🔥 {fuel_name} - {fuel_type}")
                    
                    if fuel_type == "Refined Coal":
                        print(f"      🎯 FOUND REFINED COAL - Checking percentages...")
                        
                        years_percentage = fuel.get('years_percentage', {})
                        
                        # Check some specific years
                        test_years = ['2020', '2021', '2022', '2023', '2024', '2025', '2030', '2040', '2050']
                        
                        refined_coal_issues = []
                        for year in test_years:
                            if year in years_percentage:
                                percentage = years_percentage[year]
                                print(f"         {year}: {percentage}")
                                
                                # Check if it's non-zero when it should be zero
                                if percentage > 0 and year not in ['2021']:  # 2021 might have actual generation
                                    refined_coal_issues.append(f"{year}: {percentage}")
                        
                        if refined_coal_issues:
                            print(f"      ❌ REFINED COAL ISSUES FOUND:")
                            for issue in refined_coal_issues:
                                print(f"         - {issue} (should be 0.0)")
                        else:
                            print(f"      ✅ Refined coal percentages look correct")
                
                # Check remaining useful life
                remaining_life = unit.get('remaining_useful_life')
                print(f"   🕒 Remaining useful life: {remaining_life}")
                
                if remaining_life and remaining_life != "null":
                    print(f"      ✅ Remaining useful life is set")
                else:
                    print(f"      ❌ Remaining useful life is null/missing")
        
        else:
            print(f"❌ No data found for {plant_name}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_remaining_useful_life():
    """Test that remaining useful life is calculated correctly"""
    
    print("\n🕒 TESTING REMAINING USEFUL LIFE CALCULATION")
    print("=" * 50)
    
    try:
        from excel_power_plant_tool import ExcelPowerPlantTool
        
        # Create tool instance
        tool = ExcelPowerPlantTool()
        
        # Test with a known plant
        plant_name = "Antelope Valley Station"
        
        print(f"🌱 Testing plant: {plant_name}")
        
        # Get plant data
        result = tool.get_plant_data(plant_name)

        if result and isinstance(result, list) and len(result) > 0:
            print(f"✅ Found {len(result)} units")

            remaining_life_issues = []

            # Check each unit
            for i, unit in enumerate(result, 1):
                unit_id = unit.get('unit_id', 'Unknown')
                remaining_life = unit.get('remaining_useful_life')
                unit_lifetime = unit.get('unit_lifetime')
                commencement_date = unit.get('commencement_date')
                
                print(f"\n📋 Unit {i}: {unit_id}")
                print(f"   Commencement: {commencement_date}")
                print(f"   Unit lifetime: {unit_lifetime} years")
                print(f"   Remaining useful life: {remaining_life}")
                
                if not remaining_life or remaining_life == "null":
                    remaining_life_issues.append(f"Unit {unit_id}: null remaining_useful_life")
                elif not unit_lifetime:
                    remaining_life_issues.append(f"Unit {unit_id}: null unit_lifetime")
                elif not commencement_date:
                    remaining_life_issues.append(f"Unit {unit_id}: null commencement_date")
                else:
                    print(f"   ✅ All lifetime fields are populated")
            
            if remaining_life_issues:
                print(f"\n❌ REMAINING USEFUL LIFE ISSUES:")
                for issue in remaining_life_issues:
                    print(f"   - {issue}")
                return False
            else:
                print(f"\n✅ All units have proper remaining useful life calculations")
                return True
        
        else:
            print(f"❌ No data found for {plant_name}")
            return False
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    
    print("🧪 TESTING REFINED COAL AND REMAINING USEFUL LIFE FIXES")
    print("=" * 60)
    
    # Run tests
    refined_coal_success = test_refined_coal_fix()
    remaining_life_success = test_remaining_useful_life()
    
    print("\n" + "=" * 60)
    print("🏁 TEST RESULTS")
    print("=" * 60)
    
    if refined_coal_success and remaining_life_success:
        print("✅ All tests passed!")
        print("🎯 Refined coal percentages are correct")
        print("🕒 Remaining useful life calculations are working")
        return True
    else:
        print("❌ Some tests failed:")
        if not refined_coal_success:
            print("   - Refined coal percentages need fixing")
        if not remaining_life_success:
            print("   - Remaining useful life calculations need fixing")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
