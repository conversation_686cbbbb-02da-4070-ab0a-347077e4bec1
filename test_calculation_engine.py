#!/usr/bin/env python3
"""
Test script for PowerPlantCalculationEngine
Tests all calculation methods and date field handling
"""

import sys
import os
sys.path.append('backend/src')

from agent.power_plant_calculation_engine import PowerPlantCalculationEngine, create_calculation_engine, validate_calculation_results
from excel_power_plant_tool import ExcelPowerPlantTool
import json

def test_calculation_engine():
    """Test the PowerPlantCalculationEngine with various scenarios"""
    
    print("🧪 TESTING POWERPLANT CALCULATION ENGINE")
    print("=" * 60)
    
    # Test 1: Create calculation engine
    print("\n1️⃣ Testing Calculation Engine Creation")
    try:
        calc_engine = create_calculation_engine('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH-feat-agi-tech-int-007/current state calculations.xlsx')
        print("✅ Calculation engine created successfully")
    except Exception as e:
        print(f"❌ Failed to create calculation engine: {e}")
        return
    
    # Test 2: PLF Calculations
    print("\n2️⃣ Testing PLF Calculations")
    
    # Test Case 1: Plant-level to unit-level extrapolation
    print("\n📊 PLF Case 1: Plant-level to unit-level extrapolation")
    plf_result1 = calc_engine.calculate_plf_case1_plant_to_unit(
        plant_generation_mwh=2000000,  # 2 TWh
        plant_capacity_mw=1000,        # 1 GW
        unit_capacity_mw=500           # 500 MW unit
    )
    print(f"Result: {json.dumps(plf_result1, indent=2)}")
    
    # Test Case 2: Direct unit calculation
    print("\n📊 PLF Case 2: Direct unit calculation")
    plf_result2 = calc_engine.calculate_plf_case2_direct_unit(
        unit_generation_mwh=1500000,   # 1.5 TWh
        unit_capacity_mw=500           # 500 MW
    )
    print(f"Result: {json.dumps(plf_result2, indent=2)}")
    
    # Test 3: Heat Rate & Efficiency Calculations
    print("\n3️⃣ Testing Heat Rate & Efficiency Calculations")
    
    # Heat rate from efficiency
    print("\n🔥 Heat Rate from Efficiency")
    heat_rate_result = calc_engine.calculate_heat_rate_from_efficiency(0.38)  # 38% efficiency
    print(f"Result: {json.dumps(heat_rate_result, indent=2)}")
    
    # Efficiency from fuel and generation
    print("\n⚡ Efficiency from Fuel and Generation")
    efficiency_result = calc_engine.calculate_efficiency_from_fuel_generation(
        generation_mwh=1000000,        # 1 TWh
        gcv_kcal_per_kg=6690,         # Bituminous coal GCV
        fuel_consumed_tons=500000      # 500k tonnes
    )
    print(f"Result: {json.dumps(efficiency_result, indent=2)}")
    
    # Test 4: Emission Calculations
    print("\n4️⃣ Testing Emission Calculations")
    
    # Case 1: From plant emissions
    print("\n💨 Emissions Case 1: From plant emissions")
    emissions_result1 = calc_engine.calculate_emissions_case1_from_plant_emissions(
        annual_emission_mt=2.5,        # 2.5 Mt CO2
        coal_type='bituminous'
    )
    print(f"Result: {json.dumps(emissions_result1, indent=2)}")
    
    # Case 2: From coal consumption
    print("\n🔥 Emissions Case 2: From coal consumption")
    emissions_result2 = calc_engine.calculate_emissions_case2_from_coal_consumption(
        coal_consumption_tons=500000,  # 500k tonnes
        coal_type='bituminous',
        technology='subcritical'
    )
    print(f"Result: {json.dumps(emissions_result2, indent=2)}")
    
    # Test 5: Lookup Functions
    print("\n5️⃣ Testing Lookup Functions")
    
    # Auxiliary power lookup
    aux_power = calc_engine.get_auxiliary_power(500, 'subcritical')
    print(f"Auxiliary power for 500MW subcritical: {aux_power * 100}%")
    
    # Plant efficiency lookup
    efficiency = calc_engine.get_plant_efficiency('supercritical')
    print(f"Efficiency for supercritical: {efficiency * 100}%")
    
    # Test 6: Main Calculation Interface
    print("\n6️⃣ Testing Main Calculation Interface")
    
    # Simulate unit data from web search
    unit_data = {
        'capacity': 500,
        'technology': 'subcritical',
        'coal_type': 'bituminous',
        'generation_mwh': 1500000,  # Some data available
        # Other fields missing - will be calculated
    }
    
    plant_context = {
        'plant_generation_mwh': 3000000,
        'plant_capacity_mw': 1000
    }
    
    print("\n🔧 Running comprehensive unit parameter calculations...")
    main_results = calc_engine.calculate_unit_parameters(unit_data, plant_context)
    print(f"Main calculation results: {json.dumps(main_results, indent=2)}")
    
    # Test 7: Validation
    print("\n7️⃣ Testing Validation")
    validated_results = validate_calculation_results(main_results)
    if 'validation_warnings' in validated_results:
        print(f"⚠️ Validation warnings: {validated_results['validation_warnings']}")
    else:
        print("✅ All validation checks passed")

def test_excel_date_fields():
    """Test Excel tool date field calculations"""
    
    print("\n🧪 TESTING EXCEL DATE FIELD CALCULATIONS")
    print("=" * 60)
    
    try:
        # Create Excel tool
        excel_tool = ExcelPowerPlantTool('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH-feat-agi-tech-int-007-feat-agi-tech-int-007/USA Details.xlsx')
        
        # Test with Barry plant (known data)
        print("\n📊 Testing Barry plant date calculations")
        barry_data = excel_tool.get_plant_data("Barry")
        
        if barry_data and len(barry_data) > 0:
            unit_data = barry_data[0]  # First unit
            print(f"Unit ID: {unit_data.get('unit_id')}")
            print(f"Capacity: {unit_data.get('capacity')} MW")
            print(f"Commencement Date: {unit_data.get('commencement_date')}")
            print(f"Remaining Useful Life: {unit_data.get('remaining_useful_life')}")
            print(f"Unit Lifetime: {unit_data.get('unit_lifetime')} years")
            
            # Verify calculations
            from datetime import datetime
            try:
                start_date = datetime.strptime(unit_data['commencement_date'], '%Y-%m-%dT%H:%M:%S.%fZ')
                end_date = datetime.strptime(unit_data['remaining_useful_life'], '%Y-%m-%dT%H:%M:%S.%fZ')
                calculated_lifetime = (end_date - start_date).days / 365.25
                
                print(f"✅ Date calculation verification:")
                print(f"   Start: {start_date.strftime('%Y-%m-%d')}")
                print(f"   End: {end_date.strftime('%Y-%m-%d')}")
                print(f"   Calculated lifetime: {calculated_lifetime:.2f} years")
                print(f"   Excel lifetime: {unit_data.get('unit_lifetime')} years")
                print(f"   Match: {abs(calculated_lifetime - unit_data.get('unit_lifetime', 0)) < 0.1}")
                
            except Exception as date_error:
                print(f"❌ Date calculation error: {date_error}")
        else:
            print("❌ No data found for Barry plant")
            
    except Exception as e:
        print(f"❌ Excel date field test failed: {e}")

def test_integration():
    """Test integration between calculation engine and Excel tool"""
    
    print("\n🧪 TESTING INTEGRATION")
    print("=" * 60)
    
    try:
        # Get Excel data
        excel_tool = ExcelPowerPlantTool('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH-feat-agi-tech-int-007/USA Details.xlsx')
        barry_data = excel_tool.get_plant_data("Barry")
        
        if barry_data and len(barry_data) > 0:
            unit_data = barry_data[0]
            
            # Create calculation engine
            calc_engine = create_calculation_engine('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH-feat-agi-tech-int-007/current state calculations.xlsx')
            
            # Prepare data for calculations
            calc_input = {
                'capacity': unit_data.get('capacity', 0),
                'technology': 'subcritical',  # Default
                'coal_type': 'bituminous'
            }
            
            # Run calculations
            calc_results = calc_engine.calculate_unit_parameters(calc_input)
            
            print(f"🔧 Integration test results:")
            print(f"Excel capacity: {unit_data.get('capacity')} MW")
            print(f"Excel commencement: {unit_data.get('commencement_date')}")
            print(f"Excel lifetime: {unit_data.get('unit_lifetime')} years")
            print(f"Calculated PLF: {calc_results.get('plf_unit', 'N/A')}")
            print(f"Calculated efficiency: {calc_results.get('efficiency', 'N/A')}")
            print(f"Calculated auxiliary power: {calc_results.get('auxiliary_power_percent', 'N/A')}")
            
            print("✅ Integration test completed successfully")
        else:
            print("❌ No Excel data available for integration test")
            
    except Exception as e:
        print(f"❌ Integration test failed: {e}")

if __name__ == "__main__":
    print("🚀 STARTING COMPREHENSIVE CALCULATION ENGINE TESTS")
    print("=" * 80)
    
    test_calculation_engine()
    test_excel_date_fields()
    test_integration()
    
    print("\n🏁 ALL TESTS COMPLETED")
    print("=" * 80)
