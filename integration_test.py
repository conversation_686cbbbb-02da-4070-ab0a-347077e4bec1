#!/usr/bin/env python3
"""
Comprehensive integration test for the Excel Power Plant Tool.
Tests all functionality end-to-end to ensure production readiness.
"""

import json
import time
from excel_power_plant_tool import ExcelPowerPlantTool

def test_data_loading_integrity():
    """Test data loading and integrity."""
    print("=" * 60)
    print("TESTING DATA LOADING INTEGRITY")
    print("=" * 60)
    
    tool = ExcelPowerPlantTool()
    
    # Check data completeness
    print(f"✓ USA Details loaded: {len(tool.usa_details)} units")
    print(f"✓ Coal data loaded: {len(tool.coal_data)} records")
    print(f"✓ Emission factors loaded: {len(tool.emission_factors_coal)} mappings")
    
    # Check data quality
    usa_with_capacity = tool.usa_details[tool.usa_details['Capacity'].notna()]
    print(f"✓ Units with capacity data: {len(usa_with_capacity)}")
    
    usa_with_dates = tool.usa_details[tool.usa_details['Operating Year'].notna()]
    print(f"✓ Units with operating dates: {len(usa_with_dates)}")
    
    # Check coal data years
    coal_years = sorted(tool.coal_data['Year'].unique())
    print(f"✓ Coal data years: {coal_years}")
    
    return tool

def test_schema_compliance_comprehensive(tool):
    """Comprehensive schema compliance testing."""
    print("\n" + "=" * 60)
    print("TESTING COMPREHENSIVE SCHEMA COMPLIANCE")
    print("=" * 60)
    
    test_cases = [
        ("A B Brown", "Specific plant query"),
        ("coal plants with emission data", "Coal plants with emissions"),
        ("large capacity nuclear", "Nuclear facilities"),
        ("wind turbines", "Renewable energy"),
        ("plants near retirement", "Retirement analysis")
    ]
    
    all_passed = True
    
    for query, description in test_cases:
        print(f"\nTesting: {description}")
        print(f"Query: '{query}'")
        
        results = tool.query(query)
        if not results:
            print("❌ No results returned")
            all_passed = False
            continue
        
        result = results[0]
        
        # Check required fields
        required_fields = ['sk', 'capacity', 'capacity_unit', 'commencement_date', 
                          'remaining_useful_life', 'unit_lifetime', 'unit_number', 'plant_id']
        
        missing_fields = [field for field in required_fields if field not in result]
        if missing_fields:
            print(f"❌ Missing required fields: {missing_fields}")
            all_passed = False
        else:
            print("✓ All required fields present")
        
        # Check timestamp format
        for field in ['commencement_date', 'remaining_useful_life']:
            if field in result:
                timestamp = result[field]
                if timestamp.endswith('Z') and 'T' in timestamp:
                    print(f"✓ {field} format correct")
                else:
                    print(f"❌ {field} format incorrect: {timestamp}")
                    all_passed = False
        
        # Check unit_lifetime calculation
        if 'unit_lifetime' in result and isinstance(result['unit_lifetime'], (int, float)):
            if 0 < result['unit_lifetime'] <= 100:  # Reasonable range
                print("✓ unit_lifetime in reasonable range")
            else:
                print(f"❌ unit_lifetime out of range: {result['unit_lifetime']}")
                all_passed = False
    
    return all_passed

def test_query_variety(tool):
    """Test variety of query types."""
    print("\n" + "=" * 60)
    print("TESTING QUERY VARIETY")
    print("=" * 60)
    
    query_types = {
        "Plant-specific": [
            "A B Brown plant",
            "Barry power station",
            "Allen S King facility"
        ],
        "Fuel-based": [
            "coal plants",
            "natural gas facilities",
            "nuclear power plants"
        ],
        "Capacity-based": [
            "high capacity plants",
            "small units under 10 MW",
            "large facilities over 500 MW"
        ],
        "Time-based": [
            "plants from the 1970s",
            "recently built facilities",
            "plants near retirement"
        ],
        "Technical": [
            "emission factors for coal",
            "bituminous coal plants",
            "plants with emission data"
        ]
    }
    
    for category, queries in query_types.items():
        print(f"\n{category} Queries:")
        for query in queries:
            results = tool.query(query)
            print(f"  '{query}' → {len(results)} results")
    
    return True

def test_performance_benchmarks(tool):
    """Test performance benchmarks."""
    print("\n" + "=" * 60)
    print("TESTING PERFORMANCE BENCHMARKS")
    print("=" * 60)
    
    # Test query response times
    test_queries = [
        "A B Brown",
        "coal plants with high emission factors",
        "large capacity nuclear facilities",
        "plants with bituminous coal",
        "facilities near retirement age"
    ]
    
    times = []
    for query in test_queries:
        start_time = time.time()
        results = tool.query(query)
        end_time = time.time()
        
        query_time = end_time - start_time
        times.append(query_time)
        
        print(f"Query: '{query[:30]}...' → {query_time:.3f}s ({len(results)} results)")
    
    avg_time = sum(times) / len(times)
    max_time = max(times)
    
    print(f"\nPerformance Summary:")
    print(f"  Average query time: {avg_time:.3f}s")
    print(f"  Maximum query time: {max_time:.3f}s")
    
    # Performance criteria
    if avg_time < 0.5:
        print("✅ Performance: EXCELLENT")
        return True
    elif avg_time < 1.0:
        print("✅ Performance: GOOD")
        return True
    elif avg_time < 2.0:
        print("⚠️  Performance: ACCEPTABLE")
        return True
    else:
        print("❌ Performance: NEEDS IMPROVEMENT")
        return False

def test_data_accuracy_spot_checks(tool):
    """Spot check data accuracy."""
    print("\n" + "=" * 60)
    print("TESTING DATA ACCURACY (SPOT CHECKS)")
    print("=" * 60)
    
    # Test A B Brown plant specifically
    results = tool.get_plant_data("A B Brown")
    if results:
        result = results[0]
        print("A B Brown Plant Validation:")
        print(f"  Plant ID: {result.get('plant_id')}")
        print(f"  Unit Number: {result.get('unit_number')}")
        print(f"  Capacity: {result.get('capacity')} MW")
        print(f"  Commencement: {result.get('commencement_date')}")
        
        # Check if emission data is present
        if 'emission_factor' in result and result['emission_factor']:
            print(f"  Emission factors: {len(result['emission_factor'])} years")
            latest_ef = result['emission_factor'][0]
            print(f"  Latest EF: {latest_ef['value']:.6f} kg CO2e/kWh ({latest_ef['year']})")
        
        if 'fuel_type' in result and result['fuel_type']:
            fuel_info = result['fuel_type'][0]
            print(f"  Fuel: {fuel_info['fuel']} ({fuel_info['type']})")
        
        print("✓ A B Brown data looks accurate")
    else:
        print("❌ A B Brown plant not found")
        return False
    
    return True

def test_error_handling(tool):
    """Test error handling and edge cases."""
    print("\n" + "=" * 60)
    print("TESTING ERROR HANDLING")
    print("=" * 60)
    
    edge_cases = [
        ("", "Empty query"),
        ("XYZ123NonexistentPlant", "Nonexistent plant"),
        ("!@#$%^&*()", "Special characters"),
        ("a" * 1000, "Very long query")
    ]
    
    all_handled = True
    
    for query, description in edge_cases:
        print(f"\nTesting: {description}")
        try:
            results = tool.query(query)
            print(f"✓ Handled gracefully: {len(results)} results")
        except Exception as e:
            print(f"❌ Exception occurred: {e}")
            all_handled = False
    
    return all_handled

def generate_usage_examples(tool):
    """Generate usage examples for documentation."""
    print("\n" + "=" * 60)
    print("GENERATING USAGE EXAMPLES")
    print("=" * 60)
    
    examples = [
        "A B Brown plant",
        "coal plants with emission factors",
        "large capacity nuclear facilities"
    ]
    
    for example in examples:
        print(f"\nExample Query: '{example}'")
        results = tool.query(example)
        if results:
            result = results[0]
            print("Sample Response:")
            # Show abbreviated JSON
            sample = {
                "plant_id": result.get('plant_id'),
                "unit_number": result.get('unit_number'),
                "capacity": result.get('capacity'),
                "capacity_unit": result.get('capacity_unit'),
                "commencement_date": result.get('commencement_date')
            }
            print(json.dumps(sample, indent=2))

def main():
    """Main integration test function."""
    print("Excel Power Plant Tool - Comprehensive Integration Test")
    print("=" * 60)
    
    # Initialize tool
    start_time = time.time()
    tool = test_data_loading_integrity()
    init_time = time.time() - start_time
    print(f"\n✓ Tool initialization completed in {init_time:.2f}s")
    
    # Run all tests
    tests = [
        ("Schema Compliance", test_schema_compliance_comprehensive),
        ("Query Variety", test_query_variety),
        ("Performance", test_performance_benchmarks),
        ("Data Accuracy", test_data_accuracy_spot_checks),
        ("Error Handling", test_error_handling)
    ]
    
    results = {}
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func(tool)
        except Exception as e:
            print(f"❌ {test_name} test failed with exception: {e}")
            results[test_name] = False
    
    # Generate examples
    generate_usage_examples(tool)
    
    # Final summary
    print("\n" + "=" * 60)
    print("INTEGRATION TEST SUMMARY")
    print("=" * 60)
    
    passed_tests = sum(1 for result in results.values() if result)
    total_tests = len(results)
    
    for test_name, passed in results.items():
        status = "✅ PASSED" if passed else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    print(f"\nOverall: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("🎉 ALL INTEGRATION TESTS PASSED!")
        print("✅ Tool is ready for production deployment")
    else:
        print("❌ Some tests failed - review before deployment")

if __name__ == "__main__":
    main()
