#!/usr/bin/env python3
"""
Comprehensive test script for all implemented fixes:
1. Auxiliary Power - Always use plant capacity
2. Weighted Emission Factor - Use Electric Fuel Tons
3. Simplified Remaining Useful Life - Planned retirement OR +50 years
4. Fixed Fuel Percentage Time Range - Unit-specific retirement year
5. Aggregated Net Generation - For PLF, heat rate, efficiency calculations

Test Plants:
- Bonanza Power Station
- Cardinal Power Station
- Louisa Generating Station
- Biron Mill
"""

import sys
import os
import traceback
from datetime import datetime

# Add the project root to Python path
sys.path.append('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH-feat-agi-tech-int-007')

def test_auxiliary_power_fix():
    """Test that auxiliary power uses plant capacity (not unit capacity)"""
    print("\n🔧 TESTING FIX 1: AUXILIARY POWER - PLANT CAPACITY")
    print("=" * 60)
    
    try:
        from excel_power_plant_tool import ExcelPowerPlantTool
        from usa_excel_calculation_engine import create_usa_excel_calculation_engine
        
        # Test plant with multiple units
        plant_name = "Bonanza Power Station"
        
        # Get plant data
        excel_tool = ExcelPowerPlantTool('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH-feat-agi-tech-int-007/USA Details.xlsx', 'test')
        unit_data = excel_tool.get_plant_data(plant_name)
        
        if unit_data and len(unit_data) > 0:
            first_unit = unit_data[0]
            unit_capacity = first_unit.get('capacity', 0)
            
            # Calculate plant capacity
            usa_engine = create_usa_excel_calculation_engine()
            plant_capacity = usa_engine._calculate_plant_capacity_from_excel(plant_name)
            
            print(f"Plant: {plant_name}")
            print(f"Unit Capacity: {unit_capacity} MW")
            print(f"Plant Capacity: {plant_capacity} MW")
            
            # Test auxiliary power calculation
            aux_power_unit = usa_engine.get_auxiliary_power_percentage(unit_capacity, 'subcritical')
            aux_power_plant = usa_engine.get_auxiliary_power_percentage(plant_capacity, 'subcritical')
            
            print(f"Auxiliary Power (Unit Capacity): {aux_power_unit*100:.1f}%")
            print(f"Auxiliary Power (Plant Capacity): {aux_power_plant*100:.1f}%")
            
            if plant_capacity > unit_capacity:
                if aux_power_plant != aux_power_unit:
                    print("✅ PASS: Auxiliary power correctly uses plant capacity")
                else:
                    print("⚠️ WARNING: Plant and unit capacities give same aux power")
            else:
                print("ℹ️ INFO: Plant capacity equals unit capacity for this plant")
                
        else:
            print(f"❌ FAIL: No unit data found for {plant_name}")
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        traceback.print_exc()

def test_weighted_emission_factor():
    """Test weighted emission factor calculation using Electric Fuel Tons"""
    print("\n🏭 TESTING FIX 2: WEIGHTED EMISSION FACTOR")
    print("=" * 60)
    
    try:
        from excel_power_plant_tool import ExcelPowerPlantTool
        
        # Test plant with multiple fuel types
        plant_name = "Cardinal Power Station"
        
        excel_tool = ExcelPowerPlantTool('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH-feat-agi-tech-int-007/USA Details.xlsx', 'test')
        unit_data = excel_tool.get_plant_data(plant_name)
        
        if unit_data and len(unit_data) > 0:
            first_unit = unit_data[0]
            emission_factor = first_unit.get('emission_factor')
            fuel_types = first_unit.get('fuel_type', [])
            
            print(f"Plant: {plant_name}")
            print(f"Calculated Emission Factor: {emission_factor}")
            print(f"Number of Fuel Types: {len(fuel_types)}")
            
            for i, fuel in enumerate(fuel_types):
                fuel_type = fuel.get('type', 'Unknown')
                print(f"  Fuel {i+1}: {fuel_type}")
            
            if emission_factor and emission_factor > 0:
                print("✅ PASS: Weighted emission factor calculated")
            else:
                print("❌ FAIL: No emission factor calculated")
                
        else:
            print(f"❌ FAIL: No unit data found for {plant_name}")
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        traceback.print_exc()

def test_remaining_useful_life():
    """Test simplified remaining useful life calculation"""
    print("\n⏰ TESTING FIX 3: SIMPLIFIED REMAINING USEFUL LIFE")
    print("=" * 60)
    
    try:
        from excel_power_plant_tool import ExcelPowerPlantTool
        
        # Test multiple plants
        test_plants = ["Louisa Generating Station", "Biron Mill"]
        
        excel_tool = ExcelPowerPlantTool('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH-feat-agi-tech-int-007/USA Details.xlsx', 'test')
        
        for plant_name in test_plants:
            print(f"\nPlant: {plant_name}")
            unit_data = excel_tool.get_plant_data(plant_name)
            
            if unit_data and len(unit_data) > 0:
                for unit in unit_data[:2]:  # Test first 2 units
                    unit_id = unit.get('unit_id', 'Unknown')
                    commencement_date = unit.get('commencement_date', 'Unknown')
                    remaining_life = unit.get('remaining_useful_life', 'Unknown')
                    
                    print(f"  Unit {unit_id}:")
                    print(f"    Commencement: {commencement_date}")
                    print(f"    Remaining Life: {remaining_life}")
                    
                    if remaining_life and remaining_life != 'Unknown':
                        print("    ✅ PASS: Remaining useful life calculated")
                    else:
                        print("    ❌ FAIL: No remaining useful life")
            else:
                print(f"  ❌ FAIL: No unit data found")
                
    except Exception as e:
        print(f"❌ ERROR: {e}")
        traceback.print_exc()

def test_fuel_percentage_time_range():
    """Test fuel percentage uses unit-specific time range"""
    print("\n⛽ TESTING FIX 4: FUEL PERCENTAGE TIME RANGE")
    print("=" * 60)
    
    try:
        from excel_power_plant_tool import ExcelPowerPlantTool
        
        plant_name = "Bonanza Power Station"
        
        excel_tool = ExcelPowerPlantTool('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH-feat-agi-tech-int-007/USA Details.xlsx', 'test')
        unit_data = excel_tool.get_plant_data(plant_name)
        
        if unit_data and len(unit_data) > 0:
            first_unit = unit_data[0]
            fuel_types = first_unit.get('fuel_type', [])
            commencement_date = first_unit.get('commencement_date', '')
            
            print(f"Plant: {plant_name}")
            print(f"Commencement: {commencement_date}")
            
            if fuel_types:
                fuel = fuel_types[0]
                years_percentage = fuel.get('years_percentage', {})
                
                if years_percentage:
                    years = sorted([int(y) for y in years_percentage.keys() if y.isdigit()])
                    min_year = min(years) if years else 0
                    max_year = max(years) if years else 0
                    
                    print(f"Fuel percentage years: {min_year} to {max_year}")
                    print(f"Total years covered: {len(years)}")
                    
                    # Check if it's not using fixed 2050
                    if max_year != 2050 or min_year > 2020:
                        print("✅ PASS: Using unit-specific time range (not fixed 2050)")
                    else:
                        print("⚠️ WARNING: May still be using fixed time range")
                else:
                    print("❌ FAIL: No fuel percentage data")
            else:
                print("❌ FAIL: No fuel type data")
                
        else:
            print(f"❌ FAIL: No unit data found for {plant_name}")
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        traceback.print_exc()

def test_aggregated_net_generation():
    """Test that net generation is aggregated across fuel types"""
    print("\n📊 TESTING FIX 5: AGGREGATED NET GENERATION")
    print("=" * 60)
    
    try:
        from excel_power_plant_tool import ExcelPowerPlantTool
        
        # Test plant with multiple fuel types
        plant_name = "Cardinal Power Station"
        
        excel_tool = ExcelPowerPlantTool('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH-feat-agi-tech-int-007/USA Details.xlsx', 'test')
        unit_data = excel_tool.get_plant_data(plant_name)
        
        if unit_data and len(unit_data) > 0:
            first_unit = unit_data[0]
            fuel_types = first_unit.get('fuel_type', [])
            heat_rate = first_unit.get('heat_rate')
            efficiency = first_unit.get('coal_unit_efficiency')
            
            print(f"Plant: {plant_name}")
            print(f"Number of Fuel Types: {len(fuel_types)}")
            print(f"Heat Rate: {heat_rate}")
            print(f"Efficiency: {efficiency}")
            
            if len(fuel_types) > 1:
                print("✅ PASS: Multiple fuel types found")
                if heat_rate and efficiency:
                    print("✅ PASS: Heat rate and efficiency calculated with aggregated data")
                else:
                    print("❌ FAIL: Missing heat rate or efficiency")
            else:
                print("ℹ️ INFO: Single fuel type plant")
                
        else:
            print(f"❌ FAIL: No unit data found for {plant_name}")
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        traceback.print_exc()

def main():
    """Run all tests"""
    print("🚀 COMPREHENSIVE TEST SUITE FOR ALL FIXES")
    print("=" * 80)
    print(f"Test started at: {datetime.now()}")
    
    # Run all tests
    test_auxiliary_power_fix()
    test_weighted_emission_factor()
    test_remaining_useful_life()
    test_fuel_percentage_time_range()
    test_aggregated_net_generation()
    
    print(f"\n✅ Test completed at: {datetime.now()}")
    print("=" * 80)

if __name__ == "__main__":
    main()
