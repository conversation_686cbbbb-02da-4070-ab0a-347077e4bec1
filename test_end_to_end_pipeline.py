#!/usr/bin/env python3
"""
Test the complete end-to-end pipeline to verify unit detection and processing
"""

import sys
import os
sys.path.append('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH-feat-agi-tech-int-007')

def test_unit_extraction_pipeline():
    """Test the unit extraction pipeline with USA Details integration"""
    print("🔍 TESTING END-TO-END UNIT EXTRACTION PIPELINE")
    print("=" * 60)
    
    # Simulate plant_context for Antelope Valley
    plant_context = {
        'plant_name': 'Antelope Valley Station',
        'country': 'United States',
        'plant_type': 'coal',
        'plant_id': '1',
        'plant_uid': 'test-plant-uuid-123'
    }
    
    print(f"📋 Testing with plant_context: {plant_context}")
    
    try:
        # Import the combine_unit_data function which contains the USA logic
        from backend.src.agent.unit_extraction_stages import combine_unit_data
        
        # Test with Unit 1
        print(f"\n🔧 Testing Unit 1 processing:")
        stage_results = [
            {"unit_number": "1", "capacity": 477, "technology": "subcritical"},
            {"heat_rate": 2200, "plf": []},
            {"fuel_type": ["lignite"], "emission_factor": []},
            {"capex_required_renovation": "Not available"}
        ]
        
        unit_1_data = combine_unit_data(stage_results, "1", plant_context)
        
        print(f"✅ Unit 1 data generated")
        print(f"   SK: {unit_1_data.get('sk', 'Missing')}")
        print(f"   Unit Number: {unit_1_data.get('unit_number', 'Missing')}")
        print(f"   Capacity: {unit_1_data.get('capacity', 'Missing')}")
        
        # Check if PLF arrays are populated (should be from USA calculations)
        plf_data = unit_1_data.get('plf', [])
        aux_data = unit_1_data.get('auxiliary_power_consumed', [])
        gross_data = unit_1_data.get('gross_power_generation', [])
        
        print(f"   PLF data: {len(plf_data)} records")
        print(f"   Auxiliary power data: {len(aux_data)} records")
        print(f"   Gross generation data: {len(gross_data)} records")
        
        if plf_data and len(plf_data) > 0:
            first_plf = plf_data[0]
            print(f"   First PLF record: {first_plf}")
            if isinstance(first_plf, dict) and 'value' in first_plf:
                plf_percent = first_plf['value'] * 100
                print(f"   PLF value: {plf_percent:.1f}%")
        
        # Test with Unit 2
        print(f"\n🔧 Testing Unit 2 processing:")
        unit_2_data = combine_unit_data(stage_results, "2", plant_context)
        
        print(f"✅ Unit 2 data generated")
        print(f"   SK: {unit_2_data.get('sk', 'Missing')}")
        print(f"   Unit Number: {unit_2_data.get('unit_number', 'Missing')}")
        
        # Check if both units have the same PLF (Case 1 behavior)
        unit_2_plf = unit_2_data.get('plf', [])
        if plf_data and unit_2_plf and len(plf_data) > 0 and len(unit_2_plf) > 0:
            unit_1_plf_val = plf_data[0].get('value', 0)
            unit_2_plf_val = unit_2_plf[0].get('value', 0)
            
            if abs(unit_1_plf_val - unit_2_plf_val) < 0.001:
                print(f"✅ Both units have same PLF (Case 1 behavior): {unit_1_plf_val*100:.1f}%")
            else:
                print(f"📊 Units have different PLF: Unit 1={unit_1_plf_val*100:.1f}%, Unit 2={unit_2_plf_val*100:.1f}%")
        
        # Check if plant_context was updated with USA units
        if 'usa_units' in plant_context:
            usa_units = plant_context['usa_units']
            print(f"\n✅ Plant context updated with USA units: {len(usa_units)} units")
            for unit in usa_units:
                print(f"   Unit {unit['unit_id']}: {unit['capacity_mw']} MW")
        
        if 'units_id' in plant_context:
            units_id = plant_context['units_id']
            print(f"✅ Plant context units_id: {units_id}")
            
            if len(units_id) == 2 and 1 in units_id and 2 in units_id:
                print("✅ CORRECT: Both Unit 1 and Unit 2 in units_id")
                return True
            else:
                print(f"❌ INCORRECT: Expected [1, 2] in units_id, got: {units_id}")
                return False
        else:
            print("❌ Plant context not updated with units_id")
            return False
            
    except Exception as e:
        print(f"❌ Pipeline test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_json_filename_generation():
    """Test that correct JSON filenames are generated for both units"""
    print("\n📄 TESTING JSON FILENAME GENERATION")
    print("=" * 60)
    
    # Test Unit 1
    unit_1_sk = "unit#coal#1#plant#1"
    expected_filename_1 = "unit#coal#1#plant#1.json"
    
    print(f"Unit 1 SK: {unit_1_sk}")
    print(f"Expected filename: {expected_filename_1}")
    
    # Test Unit 2
    unit_2_sk = "unit#coal#2#plant#1"
    expected_filename_2 = "unit#coal#2#plant#1.json"
    
    print(f"Unit 2 SK: {unit_2_sk}")
    print(f"Expected filename: {expected_filename_2}")
    
    # Verify they are different
    if expected_filename_1 != expected_filename_2:
        print("✅ CORRECT: Different filenames for different units")
        return True
    else:
        print("❌ INCORRECT: Same filename for different units")
        return False

def test_calculation_completeness():
    """Test that all required calculations are implemented"""
    print("\n🧮 TESTING CALCULATION COMPLETENESS")
    print("=" * 60)
    
    try:
        from usa_excel_calculation_engine import create_usa_excel_calculation_engine
        calc_engine = create_usa_excel_calculation_engine()
        
        # Check if all calculation methods exist
        required_methods = [
            'calculate_heat_rate',
            'calculate_heat_rate_from_fuel_data',
            'calculate_efficiency_from_fuel_data',
            'calculate_fuel_consumption_from_emissions',
            'calculate_emission_factor',
            'calculate_heat_from_coal',
            'calculate_electricity_from_heat',
            'calculate_plf_case1_plant_level',
            'calculate_plf_case2_unit_level',
            'calculate_plf_case4_net_to_gross',
            'get_plant_units_from_usa_details'
        ]
        
        missing_methods = []
        for method in required_methods:
            if not hasattr(calc_engine, method):
                missing_methods.append(method)
        
        if missing_methods:
            print(f"❌ Missing calculation methods: {missing_methods}")
            return False
        else:
            print(f"✅ All {len(required_methods)} calculation methods implemented")
            return True
            
    except Exception as e:
        print(f"❌ Calculation completeness test failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 END-TO-END PIPELINE TESTING")
    print("=" * 80)
    
    # Test 1: Unit extraction pipeline
    pipeline_ok = test_unit_extraction_pipeline()
    
    # Test 2: JSON filename generation
    filename_ok = test_json_filename_generation()
    
    # Test 3: Calculation completeness
    calc_ok = test_calculation_completeness()
    
    print("\n" + "=" * 80)
    print("📋 FINAL SUMMARY:")
    print(f"   Unit Extraction Pipeline: {'✅ PASS' if pipeline_ok else '❌ FAIL'}")
    print(f"   JSON Filename Generation: {'✅ PASS' if filename_ok else '❌ FAIL'}")
    print(f"   Calculation Completeness: {'✅ PASS' if calc_ok else '❌ FAIL'}")
    
    if pipeline_ok and filename_ok and calc_ok:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ Both Unit 1 and Unit 2 should be detected and processed")
        print("✅ All calculations from Excel sheets are implemented")
        print("✅ Separate JSON files will be created for each unit")
        print("\n💡 The unit saving issue should now be FIXED!")
    else:
        print("\n⚠️ SOME TESTS FAILED - ISSUES REMAIN")
        
        if not pipeline_ok:
            print("❌ Unit extraction pipeline needs fixing")
        if not filename_ok:
            print("❌ JSON filename generation needs fixing")
        if not calc_ok:
            print("❌ Missing calculation methods need implementation")
