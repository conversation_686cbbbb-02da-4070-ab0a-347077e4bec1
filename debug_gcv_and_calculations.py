#!/usr/bin/env python3
"""
Debug GCV values and heat rate calculations
"""

import sys
sys.path.append('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH-feat-agi-tech-int-007')
sys.path.append('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH-feat-agi-tech-int-007/backend/src')

from excel_power_plant_tool import ExcelPowerPlantTool
from agent.unit_extraction_stages import combine_unit_data
import pandas as pd

def debug_excel_gcv_data():
    """Debug GCV data in Excel sheet"""
    
    print("🔍 DEBUGGING GCV DATA IN EXCEL SHEET")
    print("=" * 60)
    
    try:
        # Load Excel file directly
        excel_file = '/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH-feat-agi-tech-int-007/USA Details.xlsx'
        
        # Check Full Names sheet for GCV mappings
        print("📊 Checking Full Names sheet for GCV mappings...")
        full_names_df = pd.read_excel(excel_file, sheet_name='Full Names')
        print(f"Full Names columns: {list(full_names_df.columns)}")
        
        # Look for GCV-related columns
        gcv_columns = [col for col in full_names_df.columns if 'gcv' in col.lower() or 'calorific' in col.lower()]
        print(f"GCV-related columns: {gcv_columns}")
        
        if gcv_columns:
            for col in gcv_columns:
                print(f"\n{col} values:")
                print(full_names_df[['Coal Type', col]].dropna())
        
        # Check coal type mappings
        print(f"\nCoal types in Full Names sheet:")
        if 'Coal Type' in full_names_df.columns:
            coal_types = full_names_df['Coal Type'].dropna().unique()
            for coal_type in coal_types:
                print(f"  - {coal_type}")
        
        # Check USA Details sheet for coal types
        print("\n📊 Checking USA Details sheet for coal types...")
        usa_details_df = pd.read_excel(excel_file, sheet_name='USA Details')
        print(f"USA Details columns: {list(usa_details_df.columns)}")
        
        # Look for coal type columns
        coal_type_columns = [col for col in usa_details_df.columns if 'coal' in col.lower() or 'fuel' in col.lower()]
        print(f"Coal/Fuel-related columns: {coal_type_columns}")
        
        # Check Cross Generating Station specifically
        cross_data = usa_details_df[usa_details_df['Plant Name'].str.contains('Cross', na=False)]
        if not cross_data.empty:
            print(f"\n📊 Cross Generating Station data:")
            for col in coal_type_columns:
                if col in cross_data.columns:
                    values = cross_data[col].dropna().unique()
                    print(f"  {col}: {values}")
        
    except Exception as e:
        print(f"❌ Error reading Excel file: {e}")

def debug_excel_tool_gcv_processing():
    """Debug how Excel tool processes GCV data"""
    
    print("\n🔍 DEBUGGING EXCEL TOOL GCV PROCESSING")
    print("=" * 60)
    
    try:
        # Initialize Excel tool
        excel_tool = ExcelPowerPlantTool('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH-feat-agi-tech-int-007/USA Details.xlsx')
        
        # Check what GCV mappings are loaded
        print("📊 GCV mappings loaded in Excel tool:")
        if hasattr(excel_tool, 'gcv_mappings'):
            for coal_type, gcv_value in excel_tool.gcv_mappings.items():
                print(f"  {coal_type}: {gcv_value}")
        else:
            print("  No gcv_mappings attribute found")
        
        # Check emission factors coal mappings
        print("\n📊 Emission factors coal mappings:")
        if hasattr(excel_tool, 'emission_factors_coal'):
            for coal_type, ef_value in excel_tool.emission_factors_coal.items():
                print(f"  {coal_type}: {ef_value}")
        
        # Test Cross Generating Station Unit 1
        print("\n📊 Testing Cross Generating Station Unit 1:")
        results = excel_tool.get_plant_data("Cross Generating Station", "1")
        
        if results:
            unit_data = results[0]
            
            print(f"Raw Excel data for Unit 1:")
            print(f"  gcv_coal: {unit_data.get('gcv_coal')}")
            print(f"  fuel_type: {unit_data.get('fuel_type')}")
            print(f"  emission_factor_coal: {unit_data.get('emission_factor_coal')}")
            
            # Check fuel type details
            fuel_types = unit_data.get('fuel_type', [])
            for i, fuel in enumerate(fuel_types):
                print(f"  Fuel {i+1}: {fuel}")
        
        else:
            print("  No data found for Cross Generating Station Unit 1")
        
    except Exception as e:
        print(f"❌ Error in Excel tool processing: {e}")

def debug_calculation_engine_gcv():
    """Debug calculation engine GCV handling"""
    
    print("\n🔍 DEBUGGING CALCULATION ENGINE GCV")
    print("=" * 60)
    
    try:
        from agent.power_plant_calculation_engine import create_calculation_engine
        
        # Create calculation engine
        calc_engine = create_calculation_engine()
        
        # Check GCV constants
        print("📊 GCV constants in calculation engine:")
        if hasattr(calc_engine, 'constants'):
            constants = calc_engine.constants
            if hasattr(constants, 'GCV_COAL_TYPES'):
                for coal_type, gcv_value in constants.GCV_COAL_TYPES.items():
                    print(f"  {coal_type}: {gcv_value}")
            else:
                print("  No GCV_COAL_TYPES found in constants")
        
        # Test calculation with different coal types
        test_cases = [
            {"fuel_type": [{"fuel": "Coal", "type": "Lignite"}], "capacity": 590.9},
            {"fuel_type": [{"fuel": "Coal", "type": "Bituminous"}], "capacity": 590.9},
            {"fuel_type": [{"fuel": "Coal", "type": "Sub-bituminous"}], "capacity": 590.9}
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n📊 Test case {i}: {test_case['fuel_type'][0]['type']} coal")
            
            # Test GCV calculation
            try:
                calc_results = calc_engine.calculate_all_metrics(test_case)
                print(f"  Calculated GCV: {calc_results.get('gcv_coal')}")
                print(f"  Heat rate: {calc_results.get('heat_rate')}")
            except Exception as calc_error:
                print(f"  Calculation error: {calc_error}")
        
    except Exception as e:
        print(f"❌ Error in calculation engine: {e}")

def debug_combine_unit_data():
    """Debug combine_unit_data function"""
    
    print("\n🔍 DEBUGGING COMBINE_UNIT_DATA FUNCTION")
    print("=" * 60)
    
    try:
        # Get Excel data for Cross Generating Station Unit 1
        excel_tool = ExcelPowerPlantTool('/Users/<USER>/Desktop/SE-TRANSITION-AGI-TECH-feat-agi-tech-int-007/USA Details.xlsx')
        excel_results = excel_tool.get_plant_data("Cross Generating Station", "1")
        
        if excel_results:
            excel_data = excel_results[0]
            
            print("📊 Excel data before combine_unit_data:")
            print(f"  gcv_coal: {excel_data.get('gcv_coal')}")
            print(f"  fuel_type: {excel_data.get('fuel_type')}")
            
            # Create plant context
            plant_context = {
                'excel_data': excel_data,
                'plant_name': 'Cross Generating Station',
                'country': 'United States',
                'plant_uid': 'test-uuid-1',
                'plant_type': 'coal'
            }
            
            # Call combine_unit_data
            combined_data = combine_unit_data([], "1", plant_context)
            
            print("\n📊 Combined data after combine_unit_data:")
            print(f"  gcv_coal: {combined_data.get('gcv_coal')}")
            print(f"  heat_rate: {combined_data.get('heat_rate')}")
            print(f"  plf: {len(combined_data.get('plf', []))} records")
            print(f"  auxiliary_power_consumed: {len(combined_data.get('auxiliary_power_consumed', []))} records")
            print(f"  gross_power_generation: {len(combined_data.get('gross_power_generation', []))} records")
            
            # Check fuel type in combined data
            fuel_types = combined_data.get('fuel_type', [])
            if fuel_types:
                print(f"  fuel_type in combined: {fuel_types[0]}")
        
        else:
            print("❌ No Excel data found for Cross Generating Station Unit 1")
        
    except Exception as e:
        print(f"❌ Error in combine_unit_data: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_excel_gcv_data()
    debug_excel_tool_gcv_processing()
    debug_calculation_engine_gcv()
    debug_combine_unit_data()
