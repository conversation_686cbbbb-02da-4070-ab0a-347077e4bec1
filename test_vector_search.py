#!/usr/bin/env python3
"""
Test script for enhanced vector search capabilities.
Tests semantic search and natural language query processing.
"""

import json
from excel_power_plant_tool import ExcelPowerPlantTool

def test_vector_search_initialization():
    """Test vector search initialization."""
    print("=" * 60)
    print("TESTING VECTOR SEARCH INITIALIZATION")
    print("=" * 60)
    
    try:
        tool = ExcelPowerPlantTool()
        
        if tool.vectorizer is not None:
            print("✅ Vector search initialized successfully")
            print(f"   - Vectorizer vocabulary size: {len(tool.vectorizer.vocabulary_)}")
            print(f"   - Plant vectors shape: {tool.plant_vectors.shape}")
            print(f"   - Plant descriptions count: {len(tool.plant_descriptions)}")
            return tool
        else:
            print("❌ Vector search initialization failed")
            return None
            
    except Exception as e:
        print(f"❌ Error during initialization: {e}")
        return None

def test_semantic_queries(tool):
    """Test semantic query understanding."""
    print("\n" + "=" * 60)
    print("TESTING SEMANTIC QUERY UNDERSTANDING")
    print("=" * 60)
    
    semantic_queries = [
        "Find coal power plants with high emission factors",
        "Show me large capacity nuclear facilities",
        "What plants started operating in the 1990s?",
        "List renewable energy plants with wind turbines",
        "Find plants near retirement age",
        "Show me facilities with bituminous coal",
        "What are the newest power plants?",
        "Find plants with low capacity factors"
    ]
    
    for query in semantic_queries:
        print(f"\nQuery: '{query}'")
        print("-" * 40)
        
        results = tool.query(query)
        print(f"Results found: {len(results)}")
        
        if results:
            # Show top result with similarity score if available
            top_result = results[0]
            plant_name = top_result.get('plant_id', 'Unknown')
            unit_num = top_result.get('unit_number', 'Unknown')
            capacity = top_result.get('capacity', 'Unknown')
            
            print(f"Top match: {plant_name} Unit {unit_num} ({capacity} MW)")
            
            # Show similarity score if available
            if '_similarity_score' in top_result:
                score = top_result['_similarity_score']
                print(f"Similarity score: {score:.4f}")
            
            # Show relevant details based on query
            if 'emission' in query.lower() and 'emission_factor' in top_result:
                ef_data = top_result['emission_factor']
                if ef_data:
                    latest_ef = ef_data[0]
                    print(f"Latest emission factor: {latest_ef['value']:.6f} kg CO2e/kWh ({latest_ef['year']})")
            
            if 'capacity' in query.lower() and top_result.get('capacity'):
                print(f"Capacity: {top_result['capacity']} MW")
            
            if 'coal' in query.lower() and 'fuel_type' in top_result:
                fuel_types = [f"{ft['fuel']} ({ft['type']})" for ft in top_result['fuel_type']]
                print(f"Fuel types: {', '.join(fuel_types)}")

def test_specific_plant_queries(tool):
    """Test queries for specific plants."""
    print("\n" + "=" * 60)
    print("TESTING SPECIFIC PLANT QUERIES")
    print("=" * 60)
    
    specific_queries = [
        "Tell me about A B Brown power plant",
        "What is the capacity of Sand Point facility?",
        "Show emission data for Barry plant",
        "When did Allen S King plant start operating?"
    ]
    
    for query in specific_queries:
        print(f"\nQuery: '{query}'")
        print("-" * 40)
        
        results = tool.query(query)
        print(f"Results found: {len(results)}")
        
        if results:
            result = results[0]
            print(f"Plant: {result.get('plant_id')}")
            print(f"Unit: {result.get('unit_number')}")
            print(f"Capacity: {result.get('capacity')} MW")
            print(f"Commencement: {result.get('commencement_date')}")
            
            if '_similarity_score' in result:
                print(f"Similarity: {result['_similarity_score']:.4f}")

def test_complex_queries(tool):
    """Test complex multi-criteria queries."""
    print("\n" + "=" * 60)
    print("TESTING COMPLEX MULTI-CRITERIA QUERIES")
    print("=" * 60)
    
    complex_queries = [
        "Find coal plants with capacity over 100 MW that started before 1980",
        "Show me natural gas plants with recent emission data",
        "List plants operated by Alabama Power with coal fuel",
        "Find units scheduled for retirement in the next 10 years"
    ]
    
    for query in complex_queries:
        print(f"\nQuery: '{query}'")
        print("-" * 40)
        
        results = tool.query(query)
        print(f"Results found: {len(results)}")
        
        if results:
            # Show summary of results
            capacities = [r.get('capacity', 0) for r in results if r.get('capacity')]
            if capacities:
                avg_capacity = sum(capacities) / len(capacities)
                print(f"Average capacity: {avg_capacity:.1f} MW")
            
            # Show fuel type distribution
            fuel_types = []
            for r in results:
                if 'fuel_type' in r:
                    for ft in r['fuel_type']:
                        fuel_types.append(ft.get('type', 'Unknown'))
            
            if fuel_types:
                unique_fuels = list(set(fuel_types))
                print(f"Fuel types found: {', '.join(unique_fuels[:5])}")

def test_query_performance(tool):
    """Test query performance and response times."""
    print("\n" + "=" * 60)
    print("TESTING QUERY PERFORMANCE")
    print("=" * 60)
    
    import time
    
    test_queries = [
        "coal plants",
        "high capacity facilities",
        "A B Brown plant",
        "emission factors for bituminous coal"
    ]
    
    total_time = 0
    for query in test_queries:
        start_time = time.time()
        results = tool.query(query)
        end_time = time.time()
        
        query_time = end_time - start_time
        total_time += query_time
        
        print(f"Query: '{query}'")
        print(f"  Time: {query_time:.3f}s | Results: {len(results)}")
    
    avg_time = total_time / len(test_queries)
    print(f"\nAverage query time: {avg_time:.3f}s")
    
    if avg_time < 1.0:
        print("✅ Performance: GOOD (< 1s average)")
    elif avg_time < 3.0:
        print("⚠️  Performance: ACCEPTABLE (< 3s average)")
    else:
        print("❌ Performance: SLOW (> 3s average)")

def main():
    """Main test function."""
    print("Excel Power Plant Tool - Vector Search Test Suite")
    
    # Test initialization
    tool = test_vector_search_initialization()
    
    if tool is None:
        print("Cannot proceed without successful initialization")
        return
    
    # Test semantic queries
    test_semantic_queries(tool)
    
    # Test specific plant queries
    test_specific_plant_queries(tool)
    
    # Test complex queries
    test_complex_queries(tool)
    
    # Test performance
    test_query_performance(tool)
    
    print("\n" + "=" * 60)
    print("VECTOR SEARCH TEST SUITE COMPLETE")
    print("=" * 60)

if __name__ == "__main__":
    main()
